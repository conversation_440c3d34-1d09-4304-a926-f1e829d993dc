import { test, expect } from '@playwright/test';

test.describe('Grid Rendering E2E Tests', () => {
    test.beforeEach(async ({ page }) => {
        // Go directly to the tile tool page
        await page.goto('/tile-tool');
        await page.waitForLoadState('networkidle');

        // Wait for the tool to initialize
        await page.waitForTimeout(3000);
    });

    test('should render canvas and grid visually', async ({ page }) => {
        // Check that the canvas container exists and has content
        const canvasContainer = page.locator('.canvas-container');
        await expect(canvasContainer).toBeVisible();

        // Check that a canvas element exists inside the container
        const canvas = canvasContainer.locator('canvas');
        await expect(canvas).toBeVisible();

        // Verify canvas has proper dimensions
        const canvasBox = await canvas.boundingBox();
        expect(canvasBox).toBeTruthy();
        expect(canvasBox!.width).toBeGreaterThan(400);
        expect(canvasBox!.height).toBeGreaterThan(300);

        // Check console logs for successful grid drawing
        const logs: string[] = [];
        page.on('console', msg => {
            if (msg.type() === 'log') {
                logs.push(msg.text());
            }
        });

        // Wait a bit more for any initial grid drawing
        await page.waitForTimeout(2000);

        // Find the Show Grid checkbox in the floating panel (use more specific selector)
        const floatingPanel = page.locator('.floating-panel');
        await expect(floatingPanel).toBeVisible();

        const showGridCheckbox = floatingPanel.locator('input[type="checkbox"]').first();

        // Toggle grid to trigger redraw and capture logs
        await showGridCheckbox.uncheck();
        await page.waitForTimeout(500);
        await showGridCheckbox.check();
        await page.waitForTimeout(1000);

        // Check that grid drawing was logged
        const gridDrawnLog = logs.find(log => log.includes('Grid drawn successfully'));
        expect(gridDrawnLog).toBeTruthy();

        // Verify the log shows graphics objects were created
        expect(gridDrawnLog).toMatch(/Grid drawn successfully with \d+ graphics objects/);
    });

    test('should capture grid visual regression', async ({ page }) => {
        // Wait for full initialization
        await page.waitForTimeout(3000);

        // Get the canvas element
        const canvas = page.locator('.canvas-container canvas');
        await expect(canvas).toBeVisible();

        // Take a screenshot of just the canvas area for visual verification
        const canvasBox = await canvas.boundingBox();
        expect(canvasBox).toBeTruthy();

        // Take a screenshot of the canvas area
        await page.screenshot({
            path: 'test-results/tile-grid-visual.png',
            clip: canvasBox!,
            animations: 'disabled'
        });

        // Verify the canvas has actual content by checking if it's not just a blank canvas
        const canvasDataUrl = await canvas.evaluate((canvas: HTMLCanvasElement) => {
            return canvas.toDataURL();
        });

        // A blank canvas would have a very short data URL, 
        // a canvas with grid lines should have more data
        expect(canvasDataUrl.length).toBeGreaterThan(1000);

        // The data URL should not be the same as a completely blank canvas
        expect(canvasDataUrl).not.toBe('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    });

    test('should handle grid operations without errors', async ({ page }) => {
        // Capture console errors
        const errors: string[] = [];
        page.on('console', msg => {
            if (msg.type() === 'error') {
                errors.push(msg.text());
            }
        });

        // Wait for initialization
        await page.waitForTimeout(2000);

        // Find the floating panel and grid controls
        const floatingPanel = page.locator('.floating-panel');
        await expect(floatingPanel).toBeVisible();

        const showGridCheckbox = floatingPanel.locator('input[type="checkbox"]').first();
        const gridSizeSlider = floatingPanel.locator('input[type="range"]').first();

        // Toggle grid multiple times rapidly to stress test
        for (let i = 0; i < 3; i++) {
            await showGridCheckbox.uncheck();
            await page.waitForTimeout(100);
            await showGridCheckbox.check();
            await page.waitForTimeout(100);
        }

        // Change grid size
        await gridSizeSlider.fill('16');
        await page.waitForTimeout(200);
        await gridSizeSlider.fill('64');
        await page.waitForTimeout(200);
        await gridSizeSlider.fill('32');
        await page.waitForTimeout(500);

        // Check that no errors occurred during grid operations
        const gridErrors = errors.filter(error =>
            error.includes('grid') ||
            error.includes('Graphics') ||
            error.includes('stroke') ||
            error.includes('PixiJS')
        );

        expect(gridErrors).toHaveLength(0);

        // Verify the tool is still functional
        const canvas = page.locator('.canvas-container canvas');
        await expect(canvas).toBeVisible();
    });

    test('should verify grid drawing console output', async ({ page }) => {
        // Capture detailed console logs
        const logs: string[] = [];
        page.on('console', msg => {
            if (msg.type() === 'log') {
                logs.push(msg.text());
            }
        });

        // Wait for initialization
        await page.waitForTimeout(2000);

        // Find the floating panel and force a grid redraw
        const floatingPanel = page.locator('.floating-panel');
        const showGridCheckbox = floatingPanel.locator('input[type="checkbox"]').first();

        await showGridCheckbox.uncheck();
        await page.waitForTimeout(200);
        await showGridCheckbox.check();
        await page.waitForTimeout(1000);

        // Verify grid drawing with proper dimensions
        const gridDrawingLog = logs.find(log => log.includes('Drawing grid with size:'));
        expect(gridDrawingLog).toBeTruthy();
        expect(gridDrawingLog).toMatch(/Drawing grid with size: \d+ screen: \d+ x \d+/);

        // Verify grid was successfully drawn with graphics objects
        const gridSuccessLog = logs.find(log => log.includes('Grid drawn successfully'));
        if (gridSuccessLog) {
            expect(gridSuccessLog).toMatch(/Grid drawn successfully with \d+ graphics objects/);
        } else {
            // If no success log, at least verify we have some grid-related activity
            const gridActivityLog = logs.find(log =>
                log.includes('grid') ||
                log.includes('Grid') ||
                log.includes('Drawing')
            );
            expect(gridActivityLog).toBeTruthy();
        }
    });
});
