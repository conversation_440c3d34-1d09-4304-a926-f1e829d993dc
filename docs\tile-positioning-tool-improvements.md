# Tile Positioning Tool Improvements

This document outlines suggested enhancements for the Tile Positioning Tool used in the Banana Checklist game world editor.

## 1. Smooth Animations

Add spring-based animations when dragging tiles for a more polished feel:

```typescript
import { spring } from 'svelte/motion';

// Add near other state variables
let tilePositions = spring({ x: 0, y: 0 });

function startDragging(tileIndex, mouseX, mouseY) {
  // Existing code...
  
  // Initialize spring with current position
  tilePositions.set({ 
    x: placedTiles[draggedTileIndex].x, 
    y: placedTiles[draggedTileIndex].y 
  });
}

// In pointermove handler, replace direct assignment with:
if (isDragging && draggedTileIndex >= 0) {
  tilePositions.set({ x: snappedX, y: snappedY }, { hard: false });
  
  // Use the spring values for visual position
  placedTiles[draggedTileIndex].x = $tilePositions.x;
  placedTiles[draggedTileIndex].y = $tilePositions.y;
  placedTiles = placedTiles;
  
  rebuildWorld();
}
```

## 2. Undo/Redo Functionality

Add history tracking for user actions:

```typescript
// Add to state variables
let undoStack = [];
let redoStack = [];

function saveTileState() {
  undoStack.push(JSON.stringify(placedTiles));
  redoStack = []; // Clear redo stack on new action
}

function undo() {
  if (undoStack.length > 0) {
    redoStack.push(JSON.stringify(placedTiles));
    placedTiles = JSON.parse(undoStack.pop());
    rebuildWorld();
  }
}

function redo() {
  if (redoStack.length > 0) {
    undoStack.push(JSON.stringify(placedTiles));
    placedTiles = JSON.parse(redoStack.pop());
    rebuildWorld();
  }
}

// Call saveTileState() after placeTile(), deleteTile(), and stopDragging()
```

## 3. Keyboard Shortcuts

Improve workflow efficiency with keyboard controls:

```typescript
function setupKeyboardShortcuts() {
  window.addEventListener('keydown', (e) => {
    if (!visible) return;
    
    // Undo: Ctrl+Z
    if (e.ctrlKey && e.key === 'z') {
      e.preventDefault();
      undo();
    }
    
    // Redo: Ctrl+Y or Ctrl+Shift+Z
    if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
      e.preventDefault();
      redo();
    }
    
    // Delete: Del key when hovering over tile
    if (e.key === 'Delete' && hoveredTileIndex >= 0) {
      deleteTile(hoveredTileIndex);
    }
    
    // Copy: Ctrl+C to copy selected tile properties
    if (e.ctrlKey && e.key === 'c' && hoveredTileIndex >= 0) {
      e.preventDefault();
      copyTile(hoveredTileIndex);
    }
    
    // Paste: Ctrl+V to paste tile at cursor position
    if (e.ctrlKey && e.key === 'v') {
      e.preventDefault();
      pasteTile(snappedX, snappedY);
    }
  });
}

// Call setupKeyboardShortcuts() in initializeTool()
```

## 4. Tile Rotation and Flipping

Add transformation controls for more versatile tile placement:

```typescript
// Modify placedTiles type to include rotation and flip
let placedTiles: Array<{id: number, x: number, y: number, scale: number, rotation?: number, flipX?: boolean, flipY?: boolean}> = [];

// Add to UI controls
function rotateTile(index) {
  if (index >= 0) {
    placedTiles[index].rotation = ((placedTiles[index].rotation || 0) + 90) % 360;
    placedTiles = placedTiles;
    rebuildWorld();
  }
}

function flipTileX(index) {
  if (index >= 0) {
    placedTiles[index].flipX = !placedTiles[index].flipX;
    placedTiles = placedTiles;
    rebuildWorld();
  }
}

// Update rebuildWorld() to apply rotation and flip
// Inside the forEach loop:
if (sprite) {
  if (tile.rotation) sprite.angle = tile.rotation;
  if (tile.flipX) sprite.scale.x = -Math.abs(sprite.scale.x);
  if (tile.flipY) sprite.scale.y = -Math.abs(sprite.scale.y);
}
```

## 5. Minimap for Navigation

Add a minimap for easier navigation in large scenes:

```typescript
function createMinimap() {
  const minimapContainer = new PIXI.Container();
  minimapContainer.zIndex = 200;
  minimapContainer.x = screenWidth - 220;
  minimapContainer.y = 20;
  
  const minimapBg = new PIXI.Graphics();
  minimapBg.fill({ color: 0x000000, alpha: 0.5 });
  minimapBg.rect(0, 0, 200, 150);
  minimapContainer.addChild(minimapBg);
  
  // Create minimap viewport indicator
  const viewportIndicator = new PIXI.Graphics();
  viewportIndicator.fill({ color: 0xffffff, alpha: 0.3 });
  minimapContainer.addChild(viewportIndicator);
  
  // Update minimap in ticker
  pixiApp.ticker.add(() => {
    updateMinimap(viewportIndicator);
  });
  
  pixiApp.stage.addChild(minimapContainer);
}

function updateMinimap(indicator) {
  // Calculate visible area as percentage of world size
  // Update indicator position and size
}
```

## 6. Layer Management

Add support for multiple layers (background, midground, foreground):

```typescript
// Add layer management
let layers = [
  { name: 'Background', container: new PIXI.Container(), visible: true },
  { name: 'Midground', container: new PIXI.Container(), visible: true },
  { name: 'Foreground', container: new PIXI.Container(), visible: true }
];
let activeLayerIndex = 1; // Default to midground

// Update placedTiles to include layer
let placedTiles: Array<{id: number, x: number, y: number, scale: number, layer: number}> = [];

// Add layer UI controls
function setActiveLayer(index) {
  activeLayerIndex = index;
  // Update UI to show active layer
}

function toggleLayerVisibility(index) {
  layers[index].visible = !layers[index].visible;
  layers[index].container.visible = layers[index].visible;
}
```

## 7. Export/Import Functionality

Add the ability to save and load tile layouts:

```typescript
function exportLayout() {
  const layoutData = {
    tiles: placedTiles,
    gridSize,
    worldSize: { width: worldWidth, height: worldHeight }
  };
  
  const dataStr = JSON.stringify(layoutData, null, 2);
  const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
  
  const exportName = 'banana-world-layout.json';
  const linkElement = document.createElement('a');
  linkElement.setAttribute('href', dataUri);
  linkElement.setAttribute('download', exportName);
  linkElement.click();
}

async function importLayout(file) {
  try {
    const text = await file.text();
    const layoutData = JSON.parse(text);
    
    // Validate data structure
    if (layoutData.tiles && Array.isArray(layoutData.tiles)) {
      gridSize = layoutData.gridSize || gridSize;
      placedTiles = layoutData.tiles;
      rebuildWorld();
      drawGrid();
    }
  } catch (error) {
    console.error('Failed to import layout:', error);
  }
}
```

## 8. Smart Tile Placement

Add intelligent tile placement for faster level creation:

```typescript
function autoTilePlatform(startX, startY, width, height) {
  // Clear any existing tiles in the area
  
  // Place corner tiles
  placeTile(jungleTileMap.platform.topLeft, startX, startY);
  placeTile(jungleTileMap.platform.topRight, startX + width - gridSize, startY);
  placeTile(jungleTileMap.platform.bottomLeft, startX, startY + height - gridSize);
  placeTile(jungleTileMap.platform.bottomRight, startX + width - gridSize, startY + height - gridSize);
  
  // Place edge tiles
  for (let x = startX + gridSize; x < startX + width - gridSize; x += gridSize) {
    placeTile(jungleTileMap.platform.topCenter, x, startY);
    placeTile(jungleTileMap.platform.bottomCenter, x, startY + height - gridSize);
  }
  
  for (let y = startY + gridSize; y < startY + height - gridSize; y += gridSize) {
    placeTile(jungleTileMap.platform.leftSide, startX, y);
    placeTile(jungleTileMap.platform.rightSide, startX + width - gridSize, y);
  }
  
  // Fill center with foliage
  for (let x = startX + gridSize; x < startX + width - gridSize; x += gridSize) {
    for (let y = startY + gridSize; y < startY + height - gridSize; y += gridSize) {
      // Use random foliage tiles for variety
      const foliageTiles = Object.values(jungleTileMap.foliage);
      const randomTile = foliageTiles[Math.floor(Math.random() * foliageTiles.length)];
      placeTile(randomTile, x, y);
    }
  }
}
```

## 9. Performance Optimizations

Improve performance for large scenes:

```typescript
// Add culling for large worlds
function updateVisibleTiles() {
  const visibleBounds = {
    x: -worldContainer.x,
    y: -worldContainer.y,
    width: screenWidth,
    height: screenHeight
  };
  
  // Expand bounds slightly to prevent pop-in
  const expandedBounds = {
    x: visibleBounds.x - gridSize * 2,
    y: visibleBounds.y - gridSize * 2,
    width: visibleBounds.width + gridSize * 4,
    height: visibleBounds.height + gridSize * 4
  };
  
  // Only render tiles within expanded bounds
  worldBuilder.updateVisibleTiles(expandedBounds);
}

// Call in ticker or when panning the world
```

## Implementation Priority

1. Undo/Redo Functionality (highest priority)
2. Keyboard Shortcuts
3. Tile Rotation and Flipping
4. Export/Import Functionality
5. Layer Management
6. Smart Tile Placement
7. Minimap for Navigation
8. Smooth Animations
9. Performance Optimizations (as needed)

These improvements will significantly enhance the usability and efficiency of the tile positioning tool, making level design for the Banana Checklist game faster and more intuitive.