import { test, expect } from '@playwright/test';

test.describe('Tile Positioning Tool E2E Tests', () => {
    test.beforeEach(async ({ page }) => {
        // Go directly to the tile tool page
        await page.goto('/tile-tool');
        await page.waitForLoadState('networkidle');

        // Wait for the tool to initialize
        await page.waitForTimeout(3000);
    });

    test.describe('Tool Initialization', () => {
        test('should open tile positioning tool', async ({ page }) => {
            // Check that the tool overlay is visible
            const toolOverlay = page.locator('.tile-tool-overlay');
            await expect(toolOverlay).toBeVisible();

            // Check that the canvas container is present
            const canvasContainer = page.locator('.canvas-container');
            await expect(canvasContainer).toBeVisible();

            // Check that the floating panel is visible
            const floatingPanel = page.locator('.floating-panel');
            await expect(floatingPanel).toBeVisible();
        });

        test('should display tool controls and tabs', async ({ page }) => {
            // Check that all tabs are present
            const placementTab = page.locator('.tab-button:has-text("Placement")');
            const selectionTab = page.locator('.tab-button:has-text("Selection")');
            const layersTab = page.locator('.tab-button:has-text("Layers")');
            const smartTab = page.locator('.tab-button:has-text("Smart")');

            await expect(placementTab).toBeVisible();
            await expect(selectionTab).toBeVisible();
            await expect(layersTab).toBeVisible();
            await expect(smartTab).toBeVisible();

            // Placement tab should be active by default
            await expect(placementTab).toHaveClass(/active/);
        });

        test('should display status information', async ({ page }) => {
            const statusInfo = page.locator('.status-info');
            await expect(statusInfo).toBeVisible();

            // Check for mouse coordinates
            await expect(statusInfo).toContainText('Mouse:');
            await expect(statusInfo).toContainText('Snapped:');
            await expect(statusInfo).toContainText('Tiles Placed: 0');
            await expect(statusInfo).toContainText('Active Layer: Midground');
        });

        test('should display instructions bar', async ({ page }) => {
            const instructionsBar = page.locator('.instructions-bar');
            await expect(instructionsBar).toBeVisible();

            // Check for key instructions
            await expect(instructionsBar).toContainText('Left Click: Place tile');
            await expect(instructionsBar).toContainText('Middle Click + Drag: Move tile');
            await expect(instructionsBar).toContainText('Right Click: Delete tile');
        });
    });

    test.describe('Panel Dragging', () => {
        test('should be able to drag the floating panel', async ({ page }) => {
            const floatingPanel = page.locator('.floating-panel');
            const dragHandle = page.locator('.panel-drag-handle');

            // Get initial position
            const initialBox = await floatingPanel.boundingBox();
            expect(initialBox).toBeTruthy();

            // Drag the panel to a new position
            await dragHandle.hover();
            await page.mouse.down();
            await page.mouse.move(initialBox!.x + 100, initialBox!.y + 50);
            await page.mouse.up();

            // Wait for position update
            await page.waitForTimeout(100);

            // Check that panel moved
            const newBox = await floatingPanel.boundingBox();
            expect(newBox).toBeTruthy();
            expect(newBox!.x).toBeGreaterThan(initialBox!.x);
            expect(newBox!.y).toBeGreaterThan(initialBox!.y);
        });

        test('should show drag handle with proper styling', async ({ page }) => {
            const dragHandle = page.locator('.panel-drag-handle');
            await expect(dragHandle).toBeVisible();
            await expect(dragHandle).toContainText('Tile Tools');

            // Check cursor changes on hover
            await dragHandle.hover();
            const cursor = await dragHandle.evaluate(el => getComputedStyle(el).cursor);
            expect(cursor).toBe('move');
        });
    });

    test.describe('Grid Functionality', () => {
        test('should display grid by default', async ({ page }) => {
            // Grid should be visible by default
            const showGridCheckbox = page.locator('input[type="checkbox"]:near(:text("Show Grid"))');
            await expect(showGridCheckbox).toBeChecked();
        });

        test('should render grid visually on canvas', async ({ page }) => {
            // Wait for the tool to fully initialize
            await page.waitForTimeout(2000);

            // Check that the canvas container exists and has content
            const canvasContainer = page.locator('.canvas-container');
            await expect(canvasContainer).toBeVisible();

            // Check that a canvas element exists inside the container
            const canvas = canvasContainer.locator('canvas');
            await expect(canvas).toBeVisible();

            // Verify canvas has proper dimensions
            const canvasBox = await canvas.boundingBox();
            expect(canvasBox).toBeTruthy();
            expect(canvasBox!.width).toBeGreaterThan(400);
            expect(canvasBox!.height).toBeGreaterThan(300);

            // Check console logs for successful grid drawing
            const logs: string[] = [];
            page.on('console', msg => {
                if (msg.type() === 'log') {
                    logs.push(msg.text());
                }
            });

            // Trigger a grid redraw by toggling grid visibility
            const showGridCheckbox = page.locator('input[type="checkbox"]:near(:text("Show Grid"))');
            await showGridCheckbox.uncheck();
            await page.waitForTimeout(100);
            await showGridCheckbox.check();
            await page.waitForTimeout(500);

            // Check that grid drawing was logged
            const gridDrawnLog = logs.find(log => log.includes('Grid drawn successfully'));
            expect(gridDrawnLog).toBeTruthy();

            // Verify the log shows graphics objects were created
            expect(gridDrawnLog).toMatch(/Grid drawn successfully with \d+ graphics objects/);
        });

        test('should toggle grid visibility', async ({ page }) => {
            const showGridCheckbox = page.locator('input[type="checkbox"]:near(:text("Show Grid"))');

            // Capture console logs to verify grid state changes
            const logs: string[] = [];
            page.on('console', msg => {
                if (msg.type() === 'log') {
                    logs.push(msg.text());
                }
            });

            // Uncheck to hide grid
            await showGridCheckbox.uncheck();
            await expect(showGridCheckbox).not.toBeChecked();
            await page.waitForTimeout(200);

            // Check that grid hidden message appears
            const gridHiddenLog = logs.find(log => log.includes('Grid hidden, not drawing'));
            expect(gridHiddenLog).toBeTruthy();

            // Check to show grid again
            await showGridCheckbox.check();
            await expect(showGridCheckbox).toBeChecked();
            await page.waitForTimeout(200);

            // Check that grid is drawn again
            const gridDrawnLog = logs.find(log => log.includes('Grid drawn successfully'));
            expect(gridDrawnLog).toBeTruthy();
        });

        test('should adjust grid size and redraw', async ({ page }) => {
            const gridSizeSlider = page.locator('input[type="range"]:near(:text("Grid Size"))');
            await expect(gridSizeSlider).toBeVisible();

            // Capture console logs to verify grid redraw with new size
            const logs: string[] = [];
            page.on('console', msg => {
                if (msg.type() === 'log') {
                    logs.push(msg.text());
                }
            });

            // Change grid size
            await gridSizeSlider.fill('48');
            await page.waitForTimeout(500);

            // Check that the value is reflected in the display
            const gridSizeDisplay = page.locator('span:near(input[type="range"]:near(:text("Grid Size")))');
            await expect(gridSizeDisplay).toContainText('48px');

            // Check that grid was redrawn with new size
            const gridDrawnLog = logs.find(log => log.includes('Drawing grid with size: 48'));
            expect(gridDrawnLog).toBeTruthy();
        });

        test('should render grid components correctly', async ({ page }) => {
            // Wait for initialization
            await page.waitForTimeout(2000);

            // Capture detailed console logs
            const logs: string[] = [];
            page.on('console', msg => {
                if (msg.type() === 'log') {
                    logs.push(msg.text());
                }
            });

            // Force a grid redraw to capture logs
            const showGridCheckbox = page.locator('input[type="checkbox"]:near(:text("Show Grid"))');
            await showGridCheckbox.uncheck();
            await page.waitForTimeout(100);
            await showGridCheckbox.check();
            await page.waitForTimeout(500);

            // Verify screen dimensions were calculated
            const screenDimensionsLog = logs.find(log => log.includes('Screen dimensions updated:'));
            expect(screenDimensionsLog).toBeTruthy();

            // Verify grid drawing with proper dimensions
            const gridDrawingLog = logs.find(log => log.includes('Drawing grid with size:'));
            expect(gridDrawingLog).toBeTruthy();
            expect(gridDrawingLog).toMatch(/Drawing grid with size: \d+ screen: \d+ x \d+/);

            // Verify grid was successfully drawn with multiple graphics objects
            // (should have at least 3: main grid, major grid, world boundary)
            const gridSuccessLog = logs.find(log => log.includes('Grid drawn successfully'));
            expect(gridSuccessLog).toBeTruthy();
            expect(gridSuccessLog).toMatch(/Grid drawn successfully with [3-9]+ graphics objects/);
        });

        test('should visually render grid lines (visual regression)', async ({ page }) => {
            // Wait for full initialization
            await page.waitForTimeout(3000);

            // Ensure grid is visible
            const showGridCheckbox = page.locator('input[type="checkbox"]:near(:text("Show Grid"))');
            await showGridCheckbox.check();
            await page.waitForTimeout(1000);

            // Get the canvas element
            const canvas = page.locator('.canvas-container canvas');
            await expect(canvas).toBeVisible();

            // Take a screenshot of just the canvas area for visual verification
            const canvasBox = await canvas.boundingBox();
            expect(canvasBox).toBeTruthy();

            // Take a screenshot of the canvas area
            await page.screenshot({
                path: 'test-results/tile-grid-visual.png',
                clip: canvasBox!,
                animations: 'disabled'
            });

            // Verify the canvas has actual content by checking if it's not just a blank canvas
            // We'll do this by checking the canvas data URL contains actual graphics data
            const canvasDataUrl = await canvas.evaluate((canvas: HTMLCanvasElement) => {
                return canvas.toDataURL();
            });

            // A blank canvas would have a very short data URL,
            // a canvas with grid lines should have more data
            expect(canvasDataUrl.length).toBeGreaterThan(1000);

            // The data URL should not be the same as a completely blank canvas
            // (this is a basic check that something is actually drawn)
            expect(canvasDataUrl).not.toBe('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        });

        test('should handle grid rendering errors gracefully', async ({ page }) => {
            // Capture console errors
            const errors: string[] = [];
            page.on('console', msg => {
                if (msg.type() === 'error') {
                    errors.push(msg.text());
                }
            });

            // Wait for initialization
            await page.waitForTimeout(2000);

            // Toggle grid multiple times rapidly to stress test
            const showGridCheckbox = page.locator('input[type="checkbox"]:near(:text("Show Grid"))');
            for (let i = 0; i < 5; i++) {
                await showGridCheckbox.uncheck();
                await page.waitForTimeout(50);
                await showGridCheckbox.check();
                await page.waitForTimeout(50);
            }

            // Change grid size rapidly
            const gridSizeSlider = page.locator('input[type="range"]:near(:text("Grid Size"))');
            await gridSizeSlider.fill('16');
            await page.waitForTimeout(100);
            await gridSizeSlider.fill('64');
            await page.waitForTimeout(100);
            await gridSizeSlider.fill('32');
            await page.waitForTimeout(500);

            // Check that no errors occurred during grid operations
            const gridErrors = errors.filter(error =>
                error.includes('grid') ||
                error.includes('Graphics') ||
                error.includes('stroke') ||
                error.includes('PixiJS')
            );

            expect(gridErrors).toHaveLength(0);

            // Verify the tool is still functional
            const canvas = page.locator('.canvas-container canvas');
            await expect(canvas).toBeVisible();
        });
    });

    test.describe('Tab Navigation', () => {
        test('should switch between tabs', async ({ page }) => {
            const placementTab = page.locator('.tab-button:has-text("Placement")');
            const selectionTab = page.locator('.tab-button:has-text("Selection")');
            const layersTab = page.locator('.tab-button:has-text("Layers")');
            const smartTab = page.locator('.tab-button:has-text("Smart")');

            // Switch to Selection tab
            await selectionTab.click();
            await expect(selectionTab).toHaveClass(/active/);
            await expect(placementTab).not.toHaveClass(/active/);

            // Check selection controls are visible
            const selectAllButton = page.locator('button:has-text("Select All")');
            await expect(selectAllButton).toBeVisible();

            // Switch to Layers tab
            await layersTab.click();
            await expect(layersTab).toHaveClass(/active/);

            // Check layer controls are visible
            const backgroundButton = page.locator('button:has-text("Background")');
            const midgroundButton = page.locator('button:has-text("Midground")');
            const foregroundButton = page.locator('button:has-text("Foreground")');

            await expect(backgroundButton).toBeVisible();
            await expect(midgroundButton).toBeVisible();
            await expect(foregroundButton).toBeVisible();

            // Midground should be active by default
            await expect(midgroundButton).toHaveClass(/active/);

            // Switch to Smart tab
            await smartTab.click();
            await expect(smartTab).toHaveClass(/active/);

            // Check smart placement buttons are visible
            const platformButton = page.locator('button:has-text("4x3 Platform")');
            await expect(platformButton).toBeVisible();
        });
    });

    test.describe('Tile Category Selection', () => {
        test('should change tile categories', async ({ page }) => {
            // Ensure we're on the Placement tab
            const placementTab = page.locator('.tab-button:has-text("Placement")');
            await placementTab.click();

            const categorySelect = page.locator('select:near(:text("Tile Category"))');
            await expect(categorySelect).toBeVisible();

            // Change to foliage category
            await categorySelect.selectOption('foliage');

            // Check that foliage options appear
            const foliageSelect = page.locator('select:near(:text("Foliage Type"))');
            await expect(foliageSelect).toBeVisible();

            // Change to custom category
            await categorySelect.selectOption('custom');

            // Check that custom tile ID selector appears
            const customSelect = page.locator('select:near(:text("Tile ID"))');
            await expect(customSelect).toBeVisible();
        });
    });

    test.describe('Layer Management', () => {
        test('should switch active layers', async ({ page }) => {
            // Go to Layers tab
            const layersTab = page.locator('.tab-button:has-text("Layers")');
            await layersTab.click();

            const backgroundButton = page.locator('button:has-text("Background")');
            const foregroundButton = page.locator('button:has-text("Foreground")');

            // Switch to Background layer
            await backgroundButton.click();
            await expect(backgroundButton).toHaveClass(/active/);

            // Check status shows active layer
            const statusInfo = page.locator('.status-info');
            await expect(statusInfo).toContainText('Active Layer: Background');

            // Switch to Foreground layer
            await foregroundButton.click();
            await expect(foregroundButton).toHaveClass(/active/);
            await expect(statusInfo).toContainText('Active Layer: Foreground');
        });

        test('should toggle layer visibility', async ({ page }) => {
            // Go to Layers tab
            const layersTab = page.locator('.tab-button:has-text("Layers")');
            await layersTab.click();

            // Find layer visibility checkboxes
            const backgroundCheckbox = page.locator('input[type="checkbox"]:near(:text("Background"))');
            const midgroundCheckbox = page.locator('input[type="checkbox"]:near(:text("Midground"))');

            // All layers should be visible by default
            await expect(backgroundCheckbox).toBeChecked();
            await expect(midgroundCheckbox).toBeChecked();

            // Toggle background layer visibility
            await backgroundCheckbox.uncheck();
            await expect(backgroundCheckbox).not.toBeChecked();

            // Toggle it back on
            await backgroundCheckbox.check();
            await expect(backgroundCheckbox).toBeChecked();
        });
    });

    test.describe('Tool Actions', () => {
        test('should have undo/redo buttons', async ({ page }) => {
            const undoButton = page.locator('button:has-text("Undo")');
            const redoButton = page.locator('button:has-text("Redo")');

            await expect(undoButton).toBeVisible();
            await expect(redoButton).toBeVisible();

            // Buttons should be disabled initially (no actions to undo/redo)
            await expect(undoButton).toBeDisabled();
            await expect(redoButton).toBeDisabled();
        });

        test('should have export/import functionality', async ({ page }) => {
            const exportCodeButton = page.locator('button:has-text("Export Code")');
            const exportLayoutButton = page.locator('button:has-text("Export Layout")');
            const importLabel = page.locator('label:has-text("Import Layout")');

            await expect(exportCodeButton).toBeVisible();
            await expect(exportLayoutButton).toBeVisible();
            await expect(importLabel).toBeVisible();
        });

        test('should close tool with close button', async ({ page }) => {
            const closeButton = page.locator('.panel-toggle:has-text("✕")');
            await closeButton.click();

            // Panel should be hidden
            const floatingPanel = page.locator('.floating-panel');
            await expect(floatingPanel).not.toBeVisible();

            // Show button should appear
            const showButton = page.locator('.panel-show-button');
            await expect(showButton).toBeVisible();
            await expect(showButton).toContainText('Tools');
        });
    });

    test.describe('Smart Placement', () => {
        test('should have smart placement options', async ({ page }) => {
            // Go to Smart tab
            const smartTab = page.locator('.tab-button:has-text("Smart")');
            await smartTab.click();

            // Check for various smart placement buttons
            const platform4x3 = page.locator('button:has-text("4x3 Platform")');
            const platform6x2 = page.locator('button:has-text("6x2 Platform")');
            const topEdge5x1 = page.locator('button:has-text("5x1 Top Edge")');
            const bridge8Tile = page.locator('button:has-text("8-Tile Bridge")');
            const stairs5Step = page.locator('button:has-text("5-Step Stairs")');

            await expect(platform4x3).toBeVisible();
            await expect(platform6x2).toBeVisible();
            await expect(topEdge5x1).toBeVisible();
            await expect(bridge8Tile).toBeVisible();
            await expect(stairs5Step).toBeVisible();
        });
    });
});
