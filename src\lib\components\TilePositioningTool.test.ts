import { describe, it, expect, beforeEach, vi } from 'vitest';

/**
 * Tests for TilePositioningTool component logic
 * These tests focus on the business logic and state management
 * without relying on DOM rendering or PixiJS initialization
 */

// Mock PixiJS to avoid canvas dependencies in tests
vi.mock('pixi.js', () => ({
    Application: vi.fn().mockImplementation(() => ({
        init: vi.fn().mockResolvedValue(undefined),
        canvas: document.createElement('canvas'),
        stage: {
            eventMode: 'static',
            hitArea: {},
            sortableChildren: true,
            addChild: vi.fn(),
            on: vi.fn()
        },
        screen: { width: 800, height: 600 },
        destroy: vi.fn()
    })),
    Container: vi.fn().mockImplementation(() => ({
        sortableChildren: true,
        zIndex: 0,
        visible: true,
        addChild: vi.fn(),
        removeChildren: vi.fn(),
        children: []
    })),
    Graphics: vi.fn().mockImplementation(() => ({
        setStrokeStyle: vi.fn().mockReturnThis(),
        moveTo: vi.fn().mockReturnThis(),
        lineTo: vi.fn().mockReturnThis(),
        rect: vi.fn().mockReturnThis(),
        stroke: vi.fn().mockReturnThis(),
        label: ''
    })),
    Text: vi.fn().mockImplementation(() => ({
        label: '',
        x: 0,
        y: 0
    }))
}));

// Mock tile utilities
vi.mock('$lib/utils/tileUtils', () => ({
    TilesetManager: vi.fn().mockImplementation(() => ({
        loadTileset: vi.fn().mockResolvedValue(undefined),
        getAvailableTileIds: vi.fn().mockReturnValue([0, 1, 2, 3, 4, 5]),
        createTileSprite: vi.fn().mockReturnValue({
            x: 0,
            y: 0,
            scale: { x: 1, y: 1 },
            angle: 0,
            tint: 0xffffff,
            alpha: 1
        })
    })),
    WorldBuilder: vi.fn().mockImplementation(() => ({
        addTile: vi.fn().mockReturnValue({}),
        clearWorld: vi.fn(),
        getContainer: vi.fn().mockReturnValue({
            addChild: vi.fn()
        })
    })),
    JUNGLE_TILESET_CONFIG: {}
}));

// Helper class to simulate TilePositioningTool component logic
class TilePositioningToolLogic {
    // Tool state
    selectedTileId = 0;
    gridSize = 32;
    showGrid = true;
    showTileBorders = true;
    placedTiles: Array<{
        id: number;
        x: number;
        y: number;
        scale: number;
        rotation?: number;
        flipX?: boolean;
        flipY?: boolean;
        layer?: number;
    }> = [];

    // Jungle platform tile mappings
    jungleTileMap = {
        platform: {
            topLeft: 0,
            topCenter1: 1,
            topCenter2: 2,
            topCenter3: 3,
            topRight: 5,
            leftSide1: 16,
            rightSide1: 21,
            bottomLeft: 80,
            bottomRight: 85
        },
        foliage: {
            topLeft: 17,
            topCenter: 18,
            topRight: 20,
            center: 34
        }
    };

    // Selection state
    selectedTileIndices: Set<number> = new Set();
    selectedCategory = 'platform';
    selectedTileType = 'topLeft';

    // Undo/Redo state
    undoStack: string[] = [];
    redoStack: string[] = [];
    maxUndoSteps = 50;

    // Layer management
    layers = [
        { name: 'Background', visible: true, zIndex: 1 },
        { name: 'Midground', visible: true, zIndex: 2 },
        { name: 'Foreground', visible: true, zIndex: 3 }
    ];
    activeLayerIndex = 1;

    constructor() {
        this.saveTileState(); // Save initial empty state
    }

    getSelectedTileId(): number {
        if (this.selectedCategory === 'custom') {
            return this.selectedTileId;
        }

        const category = this.jungleTileMap[this.selectedCategory as keyof typeof this.jungleTileMap];
        if (category && this.selectedTileType in category) {
            return category[this.selectedTileType as keyof typeof category];
        }

        return this.selectedTileId;
    }

    placeTile(tileId: number, x: number, y: number, scale: number): boolean {
        // Snap to grid
        const snappedX = Math.floor(x / this.gridSize) * this.gridSize;
        const snappedY = Math.floor(y / this.gridSize) * this.gridSize;

        this.placedTiles.push({
            id: tileId,
            x: snappedX,
            y: snappedY,
            scale,
            layer: this.activeLayerIndex
        });

        this.saveTileState();
        return true;
    }

    findTileAtPosition(x: number, y: number): number {
        for (let i = this.placedTiles.length - 1; i >= 0; i--) {
            const tile = this.placedTiles[i];
            const tileSize = this.gridSize;

            if (x >= tile.x && x < tile.x + tileSize &&
                y >= tile.y && y < tile.y + tileSize) {
                return i;
            }
        }
        return -1;
    }

    deleteTile(tileIndex: number): boolean {
        if (tileIndex >= 0 && tileIndex < this.placedTiles.length) {
            this.placedTiles.splice(tileIndex, 1);
            this.saveTileState();
            return true;
        }
        return false;
    }

    clearAllTiles(): void {
        this.placedTiles = [];
        this.saveTileState();
    }

    // Selection management
    toggleTileSelection(tileIndex: number): void {
        if (this.selectedTileIndices.has(tileIndex)) {
            this.selectedTileIndices.delete(tileIndex);
        } else {
            this.selectedTileIndices.add(tileIndex);
        }
    }

    clearSelection(): void {
        this.selectedTileIndices.clear();
    }

    selectAllTiles(): void {
        this.selectedTileIndices.clear();
        this.placedTiles.forEach((_, index) => this.selectedTileIndices.add(index));
    }

    deleteSelectedTiles(): number {
        if (this.selectedTileIndices.size === 0) return 0;

        const indicesToDelete = Array.from(this.selectedTileIndices).sort((a, b) => b - a);
        const deletedCount = indicesToDelete.length;

        indicesToDelete.forEach(index => {
            if (index >= 0 && index < this.placedTiles.length) {
                this.placedTiles.splice(index, 1);
            }
        });

        this.clearSelection();
        this.saveTileState();
        return deletedCount;
    }

    // Undo/Redo functionality
    saveTileState(): void {
        const currentState = JSON.stringify(this.placedTiles);
        this.undoStack.push(currentState);

        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }

        this.redoStack = [];
    }

    undo(): boolean {
        if (this.undoStack.length > 1) {
            const currentState = JSON.stringify(this.placedTiles);
            this.redoStack.push(currentState);

            this.undoStack.pop();
            const previousState = this.undoStack[this.undoStack.length - 1];
            this.placedTiles = JSON.parse(previousState);
            return true;
        }
        return false;
    }

    redo(): boolean {
        if (this.redoStack.length > 0) {
            const currentState = JSON.stringify(this.placedTiles);
            this.undoStack.push(currentState);

            const nextState = this.redoStack.pop()!;
            this.placedTiles = JSON.parse(nextState);
            return true;
        }
        return false;
    }

    // Tile transformation
    rotateTile(tileIndex: number): boolean {
        if (tileIndex >= 0 && tileIndex < this.placedTiles.length) {
            const tile = this.placedTiles[tileIndex];
            tile.rotation = ((tile.rotation || 0) + 90) % 360;
            this.saveTileState();
            return true;
        }
        return false;
    }

    flipTileX(tileIndex: number): boolean {
        if (tileIndex >= 0 && tileIndex < this.placedTiles.length) {
            const tile = this.placedTiles[tileIndex];
            tile.flipX = !tile.flipX;
            this.saveTileState();
            return true;
        }
        return false;
    }

    flipTileY(tileIndex: number): boolean {
        if (tileIndex >= 0 && tileIndex < this.placedTiles.length) {
            const tile = this.placedTiles[tileIndex];
            tile.flipY = !tile.flipY;
            this.saveTileState();
            return true;
        }
        return false;
    }

    // Layer management
    setActiveLayer(index: number): boolean {
        if (index >= 0 && index < this.layers.length) {
            this.activeLayerIndex = index;
            return true;
        }
        return false;
    }

    toggleLayerVisibility(index: number): boolean {
        if (index >= 0 && index < this.layers.length) {
            this.layers[index].visible = !this.layers[index].visible;
            return true;
        }
        return false;
    }

    // Smart placement
    createJunglePlatform(startX: number, startY: number, width: number, height: number): void {
        const tileSize = this.gridSize;

        // Clear existing tiles in area
        this.clearTilesInArea(startX, startY, width * tileSize, height * tileSize);

        // Place corner tiles
        this.placeTile(this.jungleTileMap.platform.topLeft, startX, startY, 2);
        this.placeTile(this.jungleTileMap.platform.topRight, startX + (width - 1) * tileSize, startY, 2);
        this.placeTile(this.jungleTileMap.platform.bottomLeft, startX, startY + (height - 1) * tileSize, 2);
        this.placeTile(this.jungleTileMap.platform.bottomRight, startX + (width - 1) * tileSize, startY + (height - 1) * tileSize, 2);

        // Fill center with foliage
        for (let row = 1; row < height - 1; row++) {
            for (let col = 1; col < width - 1; col++) {
                const x = startX + col * tileSize;
                const y = startY + row * tileSize;
                this.placeTile(this.jungleTileMap.foliage.center, x, y, 2);
            }
        }
    }

    clearTilesInArea(startX: number, startY: number, width: number, height: number): number {
        const tilesToRemove: number[] = [];

        this.placedTiles.forEach((tile, index) => {
            if (tile.x >= startX && tile.x < startX + width &&
                tile.y >= startY && tile.y < startY + height) {
                tilesToRemove.push(index);
            }
        });

        // Remove tiles in reverse order to maintain indices
        for (let i = tilesToRemove.length - 1; i >= 0; i--) {
            this.placedTiles.splice(tilesToRemove[i], 1);
        }

        if (tilesToRemove.length > 0) {
            this.saveTileState();
        }

        return tilesToRemove.length;
    }

    // Export functionality
    generateCode(): string {
        let code = '// Generated tile positioning code\n';
        code += '// Copy this into your createBasicWorld() or createFarmScene() function\n\n';

        this.placedTiles.forEach((tile, index) => {
            code += `worldBuilder.addTile(${tile.id}, ${tile.x}, ${tile.y}, ${tile.scale}); // Tile ${index + 1}\n`;
        });

        return code;
    }

    exportLayout() {
        return {
            tiles: this.placedTiles,
            gridSize: this.gridSize,
            worldSize: { width: 800, height: 600 },
            metadata: {
                exportDate: new Date().toISOString(),
                version: '1.0'
            }
        };
    }

    importLayout(layoutData: any): boolean {
        try {
            if (layoutData.tiles && Array.isArray(layoutData.tiles)) {
                this.saveTileState(); // Save current state for undo
                this.gridSize = layoutData.gridSize || this.gridSize;
                this.placedTiles = layoutData.tiles;
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

describe('TilePositioningTool Logic', () => {
    let tool: TilePositioningToolLogic;

    beforeEach(() => {
        tool = new TilePositioningToolLogic();
    });

    describe('Tile Selection', () => {
        it('should get correct tile ID for platform category', () => {
            tool.selectedCategory = 'platform';
            tool.selectedTileType = 'topLeft';
            expect(tool.getSelectedTileId()).toBe(0);

            tool.selectedTileType = 'topCenter1';
            expect(tool.getSelectedTileId()).toBe(1);
        });

        it('should get correct tile ID for foliage category', () => {
            tool.selectedCategory = 'foliage';
            tool.selectedTileType = 'center';
            expect(tool.getSelectedTileId()).toBe(34);
        });

        it('should get custom tile ID when category is custom', () => {
            tool.selectedCategory = 'custom';
            tool.selectedTileId = 42;
            expect(tool.getSelectedTileId()).toBe(42);
        });
    });

    describe('Tile Placement', () => {
        it('should place tile at snapped grid position', () => {
            const result = tool.placeTile(5, 17, 23, 2);

            expect(result).toBe(true);
            expect(tool.placedTiles).toHaveLength(1);
            expect(tool.placedTiles[0]).toEqual({
                id: 5,
                x: 0, // Snapped from 17 to 0 (17 / 32 = 0.53, floor = 0, * 32 = 0)
                y: 0, // Snapped from 23 to 0 (23 / 32 = 0.71, floor = 0, * 32 = 0)
                scale: 2,
                layer: 1 // Default midground layer
            });
        });

        it('should place multiple tiles', () => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);

            expect(tool.placedTiles).toHaveLength(2);
            expect(tool.placedTiles[1]).toEqual({
                id: 2,
                x: 32,
                y: 32,
                scale: 2,
                layer: 1
            });
        });

        it('should place tile on active layer', () => {
            tool.setActiveLayer(2); // Foreground
            tool.placeTile(3, 64, 64, 2);

            expect(tool.placedTiles[0].layer).toBe(2);
        });
    });

    describe('Tile Finding', () => {
        beforeEach(() => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
            tool.placeTile(3, 64, 64, 2);
        });

        it('should find tile at exact position', () => {
            expect(tool.findTileAtPosition(0, 0)).toBe(0);
            expect(tool.findTileAtPosition(32, 32)).toBe(1);
            expect(tool.findTileAtPosition(64, 64)).toBe(2);
        });

        it('should find tile within tile bounds', () => {
            expect(tool.findTileAtPosition(15, 15)).toBe(0); // Within first tile
            expect(tool.findTileAtPosition(63, 63)).toBe(1); // Within second tile
        });

        it('should return -1 for empty position', () => {
            expect(tool.findTileAtPosition(100, 100)).toBe(-1);
            expect(tool.findTileAtPosition(96, 96)).toBe(-1); // Outside all tiles
        });

        it('should return topmost tile when tiles overlap', () => {
            tool.placeTile(4, 0, 0, 2); // Place another tile at same position
            expect(tool.findTileAtPosition(0, 0)).toBe(3); // Should return the last placed tile
        });
    });

    describe('Tile Deletion', () => {
        beforeEach(() => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
            tool.placeTile(3, 64, 64, 2);
        });

        it('should delete tile by index', () => {
            const result = tool.deleteTile(1);

            expect(result).toBe(true);
            expect(tool.placedTiles).toHaveLength(2);
            expect(tool.placedTiles[0].id).toBe(1);
            expect(tool.placedTiles[1].id).toBe(3);
        });

        it('should return false for invalid index', () => {
            expect(tool.deleteTile(-1)).toBe(false);
            expect(tool.deleteTile(10)).toBe(false);
            expect(tool.placedTiles).toHaveLength(3); // No tiles deleted
        });

        it('should clear all tiles', () => {
            tool.clearAllTiles();
            expect(tool.placedTiles).toHaveLength(0);
        });
    });

    describe('Tile Selection Management', () => {
        beforeEach(() => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
            tool.placeTile(3, 64, 64, 2);
        });

        it('should toggle tile selection', () => {
            tool.toggleTileSelection(0);
            expect(tool.selectedTileIndices.has(0)).toBe(true);

            tool.toggleTileSelection(0);
            expect(tool.selectedTileIndices.has(0)).toBe(false);
        });

        it('should select multiple tiles', () => {
            tool.toggleTileSelection(0);
            tool.toggleTileSelection(2);

            expect(tool.selectedTileIndices.size).toBe(2);
            expect(tool.selectedTileIndices.has(0)).toBe(true);
            expect(tool.selectedTileIndices.has(2)).toBe(true);
        });

        it('should clear selection', () => {
            tool.toggleTileSelection(0);
            tool.toggleTileSelection(1);
            tool.clearSelection();

            expect(tool.selectedTileIndices.size).toBe(0);
        });

        it('should select all tiles', () => {
            tool.selectAllTiles();

            expect(tool.selectedTileIndices.size).toBe(3);
            expect(tool.selectedTileIndices.has(0)).toBe(true);
            expect(tool.selectedTileIndices.has(1)).toBe(true);
            expect(tool.selectedTileIndices.has(2)).toBe(true);
        });

        it('should delete selected tiles', () => {
            tool.toggleTileSelection(0);
            tool.toggleTileSelection(2);

            const deletedCount = tool.deleteSelectedTiles();

            expect(deletedCount).toBe(2);
            expect(tool.placedTiles).toHaveLength(1);
            expect(tool.placedTiles[0].id).toBe(2); // Middle tile remains
            expect(tool.selectedTileIndices.size).toBe(0); // Selection cleared
        });
    });

    describe('Undo/Redo Functionality', () => {
        it('should save initial state', () => {
            expect(tool.undoStack).toHaveLength(1);
            expect(tool.redoStack).toHaveLength(0);
        });

        it('should save state when placing tiles', () => {
            tool.placeTile(1, 0, 0, 2);
            expect(tool.undoStack).toHaveLength(2);
        });

        it('should undo tile placement', () => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);

            const result = tool.undo();
            expect(result).toBe(true);
            expect(tool.placedTiles).toHaveLength(1);
            expect(tool.placedTiles[0].id).toBe(1);
        });

        it('should redo tile placement', () => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
            tool.undo();

            const result = tool.redo();
            expect(result).toBe(true);
            expect(tool.placedTiles).toHaveLength(2);
            expect(tool.placedTiles[1].id).toBe(2);
        });

        it('should not undo when only initial state exists', () => {
            const result = tool.undo();
            expect(result).toBe(false);
            expect(tool.placedTiles).toHaveLength(0);
        });

        it('should not redo when redo stack is empty', () => {
            const result = tool.redo();
            expect(result).toBe(false);
        });

        it('should clear redo stack on new action', () => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
            tool.undo();

            expect(tool.redoStack).toHaveLength(1);

            tool.placeTile(3, 64, 64, 2); // New action should clear redo stack
            expect(tool.redoStack).toHaveLength(0);
        });
    });

    describe('Tile Transformation', () => {
        beforeEach(() => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
        });

        it('should rotate tile', () => {
            const result = tool.rotateTile(0);
            expect(result).toBe(true);
            expect(tool.placedTiles[0].rotation).toBe(90);

            tool.rotateTile(0);
            expect(tool.placedTiles[0].rotation).toBe(180);

            tool.rotateTile(0);
            tool.rotateTile(0);
            expect(tool.placedTiles[0].rotation).toBe(0); // Full rotation
        });

        it('should flip tile horizontally', () => {
            const result = tool.flipTileX(0);
            expect(result).toBe(true);
            expect(tool.placedTiles[0].flipX).toBe(true);

            tool.flipTileX(0);
            expect(tool.placedTiles[0].flipX).toBe(false);
        });

        it('should flip tile vertically', () => {
            const result = tool.flipTileY(0);
            expect(result).toBe(true);
            expect(tool.placedTiles[0].flipY).toBe(true);

            tool.flipTileY(0);
            expect(tool.placedTiles[0].flipY).toBe(false);
        });

        it('should return false for invalid tile index', () => {
            expect(tool.rotateTile(-1)).toBe(false);
            expect(tool.flipTileX(10)).toBe(false);
            expect(tool.flipTileY(10)).toBe(false);
        });
    });

    describe('Layer Management', () => {
        it('should set active layer', () => {
            const result = tool.setActiveLayer(2);
            expect(result).toBe(true);
            expect(tool.activeLayerIndex).toBe(2);
        });

        it('should return false for invalid layer index', () => {
            expect(tool.setActiveLayer(-1)).toBe(false);
            expect(tool.setActiveLayer(10)).toBe(false);
            expect(tool.activeLayerIndex).toBe(1); // Should remain unchanged
        });

        it('should toggle layer visibility', () => {
            const result = tool.toggleLayerVisibility(0);
            expect(result).toBe(true);
            expect(tool.layers[0].visible).toBe(false);

            tool.toggleLayerVisibility(0);
            expect(tool.layers[0].visible).toBe(true);
        });

        it('should return false for invalid layer visibility toggle', () => {
            expect(tool.toggleLayerVisibility(-1)).toBe(false);
            expect(tool.toggleLayerVisibility(10)).toBe(false);
        });
    });

    describe('Smart Placement', () => {
        it('should create jungle platform', () => {
            tool.createJunglePlatform(0, 0, 3, 3);

            // Should have 4 corner tiles + 1 center tile = 5 tiles
            expect(tool.placedTiles).toHaveLength(5);

            // Check corner tiles
            const topLeft = tool.placedTiles.find(t => t.x === 0 && t.y === 0);
            const topRight = tool.placedTiles.find(t => t.x === 64 && t.y === 0);
            const bottomLeft = tool.placedTiles.find(t => t.x === 0 && t.y === 64);
            const bottomRight = tool.placedTiles.find(t => t.x === 64 && t.y === 64);

            expect(topLeft?.id).toBe(tool.jungleTileMap.platform.topLeft);
            expect(topRight?.id).toBe(tool.jungleTileMap.platform.topRight);
            expect(bottomLeft?.id).toBe(tool.jungleTileMap.platform.bottomLeft);
            expect(bottomRight?.id).toBe(tool.jungleTileMap.platform.bottomRight);

            // Check center tile
            const center = tool.placedTiles.find(t => t.x === 32 && t.y === 32);
            expect(center?.id).toBe(tool.jungleTileMap.foliage.center);
        });

        it('should clear tiles in area', () => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
            tool.placeTile(3, 100, 100, 2); // Outside area

            const clearedCount = tool.clearTilesInArea(0, 0, 64, 64);

            expect(clearedCount).toBe(2);
            expect(tool.placedTiles).toHaveLength(1);
            expect(tool.placedTiles[0].id).toBe(3); // Only tile outside area remains
        });
    });

    describe('Export/Import Functionality', () => {
        beforeEach(() => {
            tool.placeTile(1, 0, 0, 2);
            tool.placeTile(2, 32, 32, 2);
        });

        it('should generate code', () => {
            const code = tool.generateCode();

            expect(code).toContain('worldBuilder.addTile(1, 0, 0, 2); // Tile 1');
            expect(code).toContain('worldBuilder.addTile(2, 32, 32, 2); // Tile 2');
            expect(code).toContain('// Generated tile positioning code');
        });

        it('should export layout', () => {
            const layout = tool.exportLayout();

            expect(layout.tiles).toHaveLength(2);
            expect(layout.gridSize).toBe(32);
            expect(layout.worldSize).toEqual({ width: 800, height: 600 });
            expect(layout.metadata.version).toBe('1.0');
            expect(layout.metadata.exportDate).toBeDefined();
        });

        it('should import valid layout', () => {
            const layoutData = {
                tiles: [
                    { id: 5, x: 64, y: 64, scale: 2, layer: 0 }
                ],
                gridSize: 16,
                metadata: { version: '1.0' }
            };

            const result = tool.importLayout(layoutData);

            expect(result).toBe(true);
            expect(tool.placedTiles).toHaveLength(1);
            expect(tool.placedTiles[0]).toEqual(layoutData.tiles[0]);
            expect(tool.gridSize).toBe(16);
        });

        it('should reject invalid layout', () => {
            const invalidLayout = { invalid: 'data' };

            const result = tool.importLayout(invalidLayout);

            expect(result).toBe(false);
            expect(tool.placedTiles).toHaveLength(2); // Original tiles preserved
        });
    });
});
