<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { browser } from '$app/environment';
  import * as PIXI from 'pixi.js';
  import { TilesetManager, WorldBuilder, JUNGLE_TILESET_CONFIG } from '$lib/utils/tileUtils';

  // Props
  export let visible = false;

  // Canvas element reference
  let canvasContainer: HTMLDivElement;
  let pixiApp: PIXI.Application;
  let toolInitialized = false;

  // Screen dimensions
  let screenWidth = 800;
  let screenHeight = 600;
  
  // Tool objects
  let tilesetManager: TilesetManager;
  let worldBuilder: WorldBuilder;
  let worldContainer: PIXI.Container;
  let gridContainer: PIXI.Container;
  let uiContainer: PIXI.Container;

  // Tool state
  let selectedTileId = 0;
  let gridSize = 32; // Default grid size (16px tiles * 2 scale)
  let showGrid = true;
  let showTileBorders = true;
  let placedTiles: Array<{id: number, x: number, y: number, scale: number, rotation?: number, flipX?: boolean, flipY?: boolean, layer?: number}> = [];
  let availableTiles: number[] = [];

  // Jungle platform tile mappings (6x6 grid)
  const jungleTileMap = {
    // Platform structure tiles (outer edges)
    platform: {
      topLeft: 0,       // (0,0)
      topCenter1: 1,    // (1,0)
      topCenter2: 2,    // (2,0)
      topCenter3: 3,    // (3,0)
      topRight: 5,      // (5,0)
      leftSide1: 16,    // (0,1)
      leftSide2: 32,    // (0,2)
      leftSide3: 48,    // (0,3)
      rightSide1: 21,   // (5,1)
      rightSide2: 37,   // (5,2)
      rightSide3: 53,   // (5,3)
      bottomLeft: 80,   // (0,5)
      bottomCenter1: 81, // (1,5)
      bottomCenter2: 82, // (2,5)
      bottomCenter3: 83, // (3,5)
      bottomRight: 85   // (5,5)
    },
    // Foliage tiles (inner area)
    foliage: {
      topLeft: 17,    // (1,1)
      topCenter: 18,  // (2,1)
      topRight: 20,   // (4,1)
      centerLeft: 33, // (1,2)
      center: 34,     // (2,2)
      centerRight: 36, // (4,2)
      bottomLeft: 65, // (1,4)
      bottomCenter: 66, // (2,4)
      bottomRight: 68 // (4,4)
    }
  };

  // Preset tile categories for easier selection
  let selectedCategory = 'platform';
  let selectedTileType = 'topLeft';

  // UI state
  let mouseX = 0;
  let mouseY = 0;
  let snappedX = 0;
  let snappedY = 0;
  let hoveredTileIndex = -1;

  // Drag state
  let isDragging = false;
  let draggedTileIndex = -1;
  let dragStartX = 0;
  let dragStartY = 0;
  let dragOffsetX = 0;
  let dragOffsetY = 0;

  // Selection state
  let selectedTileIndices: Set<number> = new Set();
  let isSelectionMode = false;
  let isDraggingSelection = false;
  let selectionDragOffsets: Map<number, {x: number, y: number}> = new Map();

  // Floating UI state
  let activeTab: 'placement' | 'selection' | 'layers' | 'smart' = 'placement';
  let showFloatingPanel = true;

  // Panel dragging state
  let isDraggingPanel = false;
  let panelX = 20;
  let panelY = 20;
  let panelDragStartX = 0;
  let panelDragStartY = 0;

  // Undo/Redo state
  let undoStack: string[] = [];
  let redoStack: string[] = [];
  const maxUndoSteps = 50; // Limit undo history to prevent memory issues

  // Copy/Paste state
  let copiedTile: {id: number, scale: number} | null = null;

  // Keyboard handler reference for cleanup
  let keydownHandler: ((e: KeyboardEvent) => void) | null = null;

  // Layer management
  let layers = [
    { name: 'Background', container: new PIXI.Container(), visible: true, zIndex: 1 },
    { name: 'Midground', container: new PIXI.Container(), visible: true, zIndex: 2 },
    { name: 'Foreground', container: new PIXI.Container(), visible: true, zIndex: 3 }
  ];
  let activeLayerIndex = 1; // Default to midground

  $: if (visible && browser && !toolInitialized) {
    initializeTool();
  }

  $: if (!visible && toolInitialized) {
    cleanup();
  }

  onDestroy(() => {
    cleanup();
  });

  function updateScreenDimensions() {
    screenWidth = window.innerWidth;
    screenHeight = window.innerHeight - 120; // Account for header and tool UI
    console.log('Screen dimensions updated:', screenWidth, 'x', screenHeight);
  }

  async function initializeTool() {
    if (toolInitialized) return;

    try {
      updateScreenDimensions();

      // Create PixiJS application
      pixiApp = new PIXI.Application();
      await pixiApp.init({
        width: screenWidth,
        height: screenHeight,
        backgroundColor: 0x2c3e50,
        antialias: true
      });

      // Add canvas to container
      canvasContainer.appendChild(pixiApp.canvas);

      // Prevent context menu on right-click
      pixiApp.canvas.addEventListener('contextmenu', (e) => {
        e.preventDefault();
      });

      // Enable z-index sorting
      pixiApp.stage.sortableChildren = true;

      // Create containers
      worldContainer = new PIXI.Container();
      worldContainer.sortableChildren = true;
      worldContainer.zIndex = 10;
      pixiApp.stage.addChild(worldContainer);

      // Set up layer containers
      layers.forEach(layer => {
        layer.container.sortableChildren = true;
        layer.container.zIndex = layer.zIndex;
        layer.container.visible = layer.visible;
        worldContainer.addChild(layer.container);
      });

      gridContainer = new PIXI.Container();
      gridContainer.zIndex = 15; // Above worldContainer (10) but below uiContainer (100)
      pixiApp.stage.addChild(gridContainer);

      uiContainer = new PIXI.Container();
      uiContainer.zIndex = 100;
      pixiApp.stage.addChild(uiContainer);

      // Initialize tileset manager
      tilesetManager = new TilesetManager(JUNGLE_TILESET_CONFIG);
      await tilesetManager.loadTileset('/assets/Tileset-Spritesheet.png');

      // Get available tiles
      availableTiles = tilesetManager.getAvailableTileIds();

      // Initialize world builder
      worldBuilder = new WorldBuilder(tilesetManager);
      const tileContainer = worldBuilder.getContainer();
      worldContainer.addChild(tileContainer);

      // Set up interaction
      setupInteraction();

      // Set up keyboard shortcuts
      setupKeyboardShortcuts();

      // Draw initial grid
      drawGrid();

      // Save initial state for undo
      saveTileState();

      toolInitialized = true;
      console.log('Tile positioning tool initialized!');
    } catch (error) {
      console.error('Failed to initialize tile positioning tool:', error);
    }
  }

  function setupInteraction() {
    // Make stage interactive
    pixiApp.stage.eventMode = 'static';
    pixiApp.stage.hitArea = pixiApp.screen;

    // Mouse move for preview and dragging
    pixiApp.stage.on('pointermove', (event) => {
      const globalPos = event.global;
      mouseX = globalPos.x;
      mouseY = globalPos.y;

      // Snap to grid
      snappedX = Math.floor(mouseX / gridSize) * gridSize;
      snappedY = Math.floor(mouseY / gridSize) * gridSize;

      if (isDragging && draggedTileIndex >= 0) {
        // Update dragged tile position - snap to grid during drag
        placedTiles[draggedTileIndex].x = snappedX;
        placedTiles[draggedTileIndex].y = snappedY;
        placedTiles = placedTiles; // Trigger reactivity

        // Update the visual tile position
        rebuildWorld();
      } else if (isDraggingSelection) {
        // Update all selected tiles positions during group drag
        selectedTileIndices.forEach(index => {
          const offset = selectionDragOffsets.get(index);
          if (offset) {
            const newX = Math.floor((mouseX + offset.x) / gridSize) * gridSize;
            const newY = Math.floor((mouseY + offset.y) / gridSize) * gridSize;
            placedTiles[index].x = newX;
            placedTiles[index].y = newY;
          }
        });
        placedTiles = placedTiles; // Trigger reactivity
        rebuildWorld();
      } else {
        // Check for hovered tile
        hoveredTileIndex = findTileAtPosition(snappedX, snappedY);

        updatePreview();

        // Update tile borders to show hover effect
        if (showTileBorders) {
          drawTileBorders();
        }
      }
    });

    // Mouse down - handle placement, selection, and dragging
    pixiApp.stage.on('pointerdown', (event) => {
      const globalPos = event.global;
      const x = Math.floor(globalPos.x / gridSize) * gridSize;
      const y = Math.floor(globalPos.y / gridSize) * gridSize;
      const tileIndex = findTileAtPosition(x, y);

      if (event.button === 1) { // Middle mouse button - Move tiles
        if (selectedTileIndices.size > 0) {
          // Move selected tiles
          startDraggingSelection(globalPos.x, globalPos.y);
        } else if (tileIndex >= 0) {
          // Move single tile
          startDragging(tileIndex, globalPos.x, globalPos.y);
        }
      } else if (event.button === 0) { // Left mouse button
        if (event.ctrlKey && tileIndex >= 0) {
          // Ctrl + Left click: Toggle tile selection
          toggleTileSelection(tileIndex);
        } else if (!isDragging) {
          // Regular left click: Place tile and clear selection
          clearSelection();
          const tileId = getSelectedTileId();
          placeTile(tileId, x, y, 2);
        }
      } else if (event.button === 2) { // Right mouse button
        if (selectedTileIndices.size > 0 && selectedTileIndices.has(tileIndex)) {
          // Delete selected tiles if right-clicking on a selected tile
          deleteSelectedTiles();
        } else if (tileIndex >= 0) {
          // Delete single tile
          deleteTile(tileIndex);
        }
      }
    });

    // Mouse up - stop dragging
    pixiApp.stage.on('pointerup', (event) => {
      if (event.button === 1 && (isDragging || isDraggingSelection)) { // Middle mouse button
        stopDragging();
      }
    });

    // Handle mouse leave to stop dragging
    pixiApp.stage.on('pointerleave', () => {
      if (isDragging || isDraggingSelection) {
        stopDragging();
      }
    });
  }

  function drawGrid() {
    gridContainer.removeChildren();

    if (!showGrid) {
      console.log('Grid hidden, not drawing');
      return;
    }

    console.log('Drawing grid with size:', gridSize, 'screen:', screenWidth, 'x', screenHeight);

    try {
      // Create main grid graphics using modern PixiJS v8 API
      const gridGraphics = new PIXI.Graphics();

      // Draw main grid lines (every gridSize pixels)
      // Vertical lines
      for (let x = 0; x <= screenWidth; x += gridSize) {
        gridGraphics.moveTo(x, 0).lineTo(x, screenHeight);
      }

      // Horizontal lines
      for (let y = 0; y <= screenHeight; y += gridSize) {
        gridGraphics.moveTo(0, y).lineTo(screenWidth, y);
      }

      // Apply stroke style to all lines at once
      gridGraphics.stroke({ width: 1, color: 0x34495e, alpha: 0.6 });

      // Draw major grid lines (every 5 grid units) as a separate graphics object
      const majorGridGraphics = new PIXI.Graphics();

      // Major vertical lines
      for (let x = 0; x <= screenWidth; x += gridSize * 5) {
        majorGridGraphics.moveTo(x, 0).lineTo(x, screenHeight);
      }

      // Major horizontal lines
      for (let y = 0; y <= screenHeight; y += gridSize * 5) {
        majorGridGraphics.moveTo(0, y).lineTo(screenWidth, y);
      }

      // Apply stroke style to all major lines at once
      majorGridGraphics.stroke({ width: 2, color: 0x3498db, alpha: 0.5 });

      gridContainer.addChild(gridGraphics);
      gridContainer.addChild(majorGridGraphics);

      // Add world boundary indicator
      const worldBoundary = new PIXI.Graphics();
      const worldWidth = Math.min(1600, screenWidth);
      const worldHeight = Math.min(900, screenHeight);

      worldBoundary
        .rect(0, 0, worldWidth, worldHeight)
        .stroke({ width: 3, color: 0xe74c3c, alpha: 0.8 });

      gridContainer.addChild(worldBoundary);

      console.log('Grid drawn successfully with', gridContainer.children.length, 'graphics objects');

    } catch (error) {
      console.error('Error drawing grid:', error);
    }
  }

  function updatePreview() {
    // Remove existing preview
    const existingPreview = uiContainer.children.find(child => child.label === 'preview');
    if (existingPreview) {
      uiContainer.removeChild(existingPreview);
    }

    // Don't show preview when dragging
    if (isDragging) {
      return;
    }

    // Create new preview
    const tileId = getSelectedTileId();
    const previewSprite = tilesetManager.createTileSprite(tileId, 2);
    if (previewSprite) {
      previewSprite.label = 'preview';
      previewSprite.x = snappedX;
      previewSprite.y = snappedY;
      previewSprite.alpha = 0.6;
      previewSprite.tint = 0x3498db; // Blue tint for preview
      uiContainer.addChild(previewSprite);
    }
  }

  function placeTile(tileId: number, x: number, y: number, scale: number) {
    const sprite = worldBuilder.addTile(tileId, x, y, scale);
    if (sprite) {
      placedTiles.push({ id: tileId, x, y, scale, layer: activeLayerIndex });
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState(); // Save state for undo
      console.log(`Placed tile ${tileId} at (${x}, ${y}) on layer ${layers[activeLayerIndex].name}`);
    }
  }

  function findTileAtPosition(x: number, y: number): number {
    for (let i = placedTiles.length - 1; i >= 0; i--) {
      const tile = placedTiles[i];
      const tileSize = gridSize; // Assuming all tiles use the current grid size

      if (x >= tile.x && x < tile.x + tileSize &&
          y >= tile.y && y < tile.y + tileSize) {
        return i;
      }
    }
    return -1;
  }

  function startDragging(tileIndex: number, mouseX: number, mouseY: number) {
    isDragging = true;
    draggedTileIndex = tileIndex;
    dragStartX = mouseX;
    dragStartY = mouseY;

    // No offset needed since we'll snap directly to grid position
    dragOffsetX = 0;
    dragOffsetY = 0;

    const tile = placedTiles[tileIndex];
    console.log(`Started dragging tile ${tileIndex} at (${tile.x}, ${tile.y})`);
  }

  function startDraggingSelection(mouseX: number, mouseY: number) {
    isDraggingSelection = true;
    dragStartX = mouseX;
    dragStartY = mouseY;

    // Store initial offsets for each selected tile
    selectionDragOffsets.clear();
    selectedTileIndices.forEach(index => {
      const tile = placedTiles[index];
      selectionDragOffsets.set(index, {
        x: tile.x - mouseX,
        y: tile.y - mouseY
      });
    });

    console.log(`Started dragging ${selectedTileIndices.size} selected tiles`);
  }

  function stopDragging() {
    if (isDragging && draggedTileIndex >= 0) {
      const tile = placedTiles[draggedTileIndex];
      console.log(`Stopped dragging tile ${draggedTileIndex} at (${tile.x}, ${tile.y})`);

      // Save state for undo after drag operation
      saveTileState();

      // Final rebuild to remove drag highlighting
      rebuildWorld();
    }

    if (isDraggingSelection) {
      console.log(`Stopped dragging ${selectedTileIndices.size} selected tiles`);

      // Save state for undo after group drag operation
      saveTileState();

      // Final rebuild to remove drag highlighting
      rebuildWorld();
    }

    isDragging = false;
    isDraggingSelection = false;
    draggedTileIndex = -1;
    dragStartX = 0;
    dragStartY = 0;
    dragOffsetX = 0;
    dragOffsetY = 0;
    selectionDragOffsets.clear();
  }

  function rebuildWorld() {
    // Clear all layer containers
    layers.forEach(layer => {
      layer.container.removeChildren();
    });

    placedTiles.forEach((tile, index) => {
      // Create sprite using tilesetManager directly
      const sprite = tilesetManager.createTileSprite(tile.id, tile.scale);

      if (sprite) {
        sprite.x = tile.x;
        sprite.y = tile.y;

        // Apply transformations
        if (tile.rotation) {
          sprite.angle = tile.rotation;
        }

        // Apply flipping by adjusting scale
        if (tile.flipX) {
          sprite.scale.x = -Math.abs(sprite.scale.x);
        }
        if (tile.flipY) {
          sprite.scale.y = -Math.abs(sprite.scale.y);
        }

        // Apply visual effects for different states
        if (isDragging && index === draggedTileIndex) {
          sprite.tint = 0xffff00; // Yellow tint for dragged tile
          sprite.alpha = 0.8;
        } else if (isDraggingSelection && selectedTileIndices.has(index)) {
          sprite.tint = 0xff8c00; // Orange tint for dragged selection
          sprite.alpha = 0.8;
        } else if (selectedTileIndices.has(index)) {
          sprite.tint = 0x00ff00; // Green tint for selected tiles
          sprite.alpha = 0.9;
        }

        // Add to appropriate layer (default to midground if layer not specified)
        const layerIndex = tile.layer !== undefined ? tile.layer : 1;
        if (layerIndex >= 0 && layerIndex < layers.length) {
          layers[layerIndex].container.addChild(sprite);
        }
      }
    });

    // Draw tile borders if enabled
    if (showTileBorders) {
      drawTileBorders();
    }
  }

  function drawTileBorders() {
    // Remove existing tile borders
    const existingBorders = uiContainer.children.filter(child => child.label === 'tile-border');
    existingBorders.forEach(border => uiContainer.removeChild(border));

    // Draw borders around each placed tile
    placedTiles.forEach((tile, index) => {
      const borderGraphics = new PIXI.Graphics();
      borderGraphics.label = 'tile-border';

      // Different border colors for different states
      let borderColor = 0x3498db; // Default blue
      let borderAlpha = 0.3;
      let borderWidth = 1;

      if (isDragging && index === draggedTileIndex) {
        borderColor = 0xf39c12; // Orange for dragged tile
        borderAlpha = 0.9;
        borderWidth = 3;
      } else if (index === hoveredTileIndex && !isDragging) {
        borderColor = 0x2ecc71; // Green for hovered tile
        borderAlpha = 0.7;
        borderWidth = 2;
      }

      borderGraphics
        .rect(tile.x, tile.y, gridSize, gridSize)
        .stroke({ width: borderWidth, color: borderColor, alpha: borderAlpha });

      uiContainer.addChild(borderGraphics);
    });

    // Add coordinate labels for hovered or dragged tiles
    if (hoveredTileIndex >= 0 || draggedTileIndex >= 0) {
      const targetIndex = isDragging ? draggedTileIndex : hoveredTileIndex;
      const tile = placedTiles[targetIndex];

      if (tile) {
        const coordText = new PIXI.Text({
          text: `(${tile.x}, ${tile.y})`,
          style: {
            fontFamily: 'Arial',
            fontSize: 12,
            fill: 0xFFFFFF,
            stroke: { color: 0x000000, width: 2 }
          }
        });

        coordText.label = 'tile-border';
        coordText.x = tile.x + gridSize + 5;
        coordText.y = tile.y - 5;

        uiContainer.addChild(coordText);
      }
    }
  }

  function getSelectedTileId(): number {
    if (selectedCategory === 'custom') {
      return selectedTileId;
    }

    const category = jungleTileMap[selectedCategory as keyof typeof jungleTileMap];
    if (category && selectedTileType in category) {
      return category[selectedTileType as keyof typeof category];
    }

    return selectedTileId;
  }

  function createJunglePlatform(startX: number, startY: number, width: number, height: number) {
    autoTilePlatform(startX, startY, width, height);
  }

  // Smart tile placement - Auto-platform creation
  function autoTilePlatform(startX: number, startY: number, width: number, height: number) {
    const tileSize = gridSize;

    // Clear any existing tiles in the area first
    clearTilesInArea(startX, startY, width * tileSize, height * tileSize);

    // Place corner tiles
    placeTile(jungleTileMap.platform.topLeft, startX, startY, 2);
    placeTile(jungleTileMap.platform.topRight, startX + (width - 1) * tileSize, startY, 2);
    placeTile(jungleTileMap.platform.bottomLeft, startX, startY + (height - 1) * tileSize, 2);
    placeTile(jungleTileMap.platform.bottomRight, startX + (width - 1) * tileSize, startY + (height - 1) * tileSize, 2);

    // Place top and bottom edge tiles with random variations
    for (let col = 1; col < width - 1; col++) {
      const x = startX + col * tileSize;

      // Random top center variation
      const topCenterVariations = [
        jungleTileMap.platform.topCenter1,
        jungleTileMap.platform.topCenter2,
        jungleTileMap.platform.topCenter3
      ];
      const randomTopCenter = topCenterVariations[Math.floor(Math.random() * topCenterVariations.length)];
      placeTile(randomTopCenter, x, startY, 2);

      // Random bottom center variation
      const bottomCenterVariations = [
        jungleTileMap.platform.bottomCenter1,
        jungleTileMap.platform.bottomCenter2,
        jungleTileMap.platform.bottomCenter3
      ];
      const randomBottomCenter = bottomCenterVariations[Math.floor(Math.random() * bottomCenterVariations.length)];
      placeTile(randomBottomCenter, x, startY + (height - 1) * tileSize, 2);
    }

    // Place left and right edge tiles with random variations
    for (let row = 1; row < height - 1; row++) {
      const y = startY + row * tileSize;

      // Random left side variation
      const leftSideVariations = [
        jungleTileMap.platform.leftSide1,
        jungleTileMap.platform.leftSide2,
        jungleTileMap.platform.leftSide3
      ];
      const randomLeftSide = leftSideVariations[Math.floor(Math.random() * leftSideVariations.length)];
      placeTile(randomLeftSide, startX, y, 2);

      // Random right side variation
      const rightSideVariations = [
        jungleTileMap.platform.rightSide1,
        jungleTileMap.platform.rightSide2,
        jungleTileMap.platform.rightSide3
      ];
      const randomRightSide = rightSideVariations[Math.floor(Math.random() * rightSideVariations.length)];
      placeTile(randomRightSide, startX + (width - 1) * tileSize, y, 2);
    }

    // Fill center with varied foliage for natural look
    for (let row = 1; row < height - 1; row++) {
      for (let col = 1; col < width - 1; col++) {
        const x = startX + col * tileSize;
        const y = startY + row * tileSize;

        // Use random foliage tiles for variety
        const foliageTiles = Object.values(jungleTileMap.foliage);
        const randomTile = foliageTiles[Math.floor(Math.random() * foliageTiles.length)];
        placeTile(randomTile, x, y, 2);
      }
    }

    console.log(`Created ${width}x${height} platform at (${startX}, ${startY})`);
  }

  function clearTilesInArea(startX: number, startY: number, width: number, height: number) {
    const tilesToRemove: number[] = [];

    placedTiles.forEach((tile, index) => {
      if (tile.x >= startX && tile.x < startX + width &&
          tile.y >= startY && tile.y < startY + height) {
        tilesToRemove.push(index);
      }
    });

    // Remove tiles in reverse order to maintain indices
    for (let i = tilesToRemove.length - 1; i >= 0; i--) {
      placedTiles.splice(tilesToRemove[i], 1);
    }

    if (tilesToRemove.length > 0) {
      placedTiles = placedTiles; // Trigger reactivity
      rebuildWorld();
      console.log(`Cleared ${tilesToRemove.length} tiles from area`);
    }
  }

  // Additional smart placement functions
  function createHorizontalPlatform(startX: number, startY: number, length: number) {
    autoTilePlatform(startX, startY, length, 1);
  }

  function createVerticalPlatform(startX: number, startY: number, height: number) {
    autoTilePlatform(startX, startY, 1, height);
  }

  function createStaircase(startX: number, startY: number, steps: number, direction: 'up' | 'down' = 'up') {
    for (let i = 0; i < steps; i++) {
      const x = startX + i * gridSize;
      const y = direction === 'up'
        ? startY - i * gridSize
        : startY + i * gridSize;

      // Create a single tile platform for each step
      placeTile(jungleTileMap.platform.topLeft, x, y, 2);
    }

    console.log(`Created ${steps}-step staircase going ${direction} at (${startX}, ${startY})`);
  }

  function create5x1Platform(startX: number, startY: number) {
    const tileSize = gridSize;

    // Clear any existing tiles in the area first
    clearTilesInArea(startX, startY, 5 * tileSize, tileSize);

    // Place the 5 tiles in sequence: Top Left, Top Center 1, Top Center 2, Top Center 3, Top Right
    placeTile(jungleTileMap.platform.topLeft, startX, startY, 2);
    placeTile(jungleTileMap.platform.topCenter1, startX + tileSize, startY, 2);
    placeTile(jungleTileMap.platform.topCenter2, startX + tileSize * 2, startY, 2);
    placeTile(jungleTileMap.platform.topCenter3, startX + tileSize * 3, startY, 2);
    placeTile(jungleTileMap.platform.topRight, startX + tileSize * 4, startY, 2);

    console.log(`Created 5x1 top edge platform at (${startX}, ${startY})`);
  }

  function create5x1BottomPlatform(startX: number, startY: number) {
    const tileSize = gridSize;

    // Clear any existing tiles in the area first
    clearTilesInArea(startX, startY, 5 * tileSize, tileSize);

    // Place the 5 tiles in sequence: Bottom Left, Bottom Center 1, Bottom Center 2, Bottom Center 3, Bottom Right
    placeTile(jungleTileMap.platform.bottomLeft, startX, startY, 2);
    placeTile(jungleTileMap.platform.bottomCenter1, startX + tileSize, startY, 2);
    placeTile(jungleTileMap.platform.bottomCenter2, startX + tileSize * 2, startY, 2);
    placeTile(jungleTileMap.platform.bottomCenter3, startX + tileSize * 3, startY, 2);
    placeTile(jungleTileMap.platform.bottomRight, startX + tileSize * 4, startY, 2);

    console.log(`Created 5x1 bottom edge platform at (${startX}, ${startY})`);
  }

  function create1x3LeftPlatform(startX: number, startY: number) {
    const tileSize = gridSize;

    // Clear any existing tiles in the area first
    clearTilesInArea(startX, startY, tileSize, 3 * tileSize);

    // Place the 3 tiles vertically: Left Side 1, Left Side 2, Left Side 3
    placeTile(jungleTileMap.platform.leftSide1, startX, startY, 2);
    placeTile(jungleTileMap.platform.leftSide2, startX, startY + tileSize, 2);
    placeTile(jungleTileMap.platform.leftSide3, startX, startY + tileSize * 2, 2);

    console.log(`Created 1x3 left edge platform at (${startX}, ${startY})`);
  }

  function create1x3RightPlatform(startX: number, startY: number) {
    const tileSize = gridSize;

    // Clear any existing tiles in the area first
    clearTilesInArea(startX, startY, tileSize, 3 * tileSize);

    // Place the 3 tiles vertically: Right Side 1, Right Side 2, Right Side 3
    placeTile(jungleTileMap.platform.rightSide1, startX, startY, 2);
    placeTile(jungleTileMap.platform.rightSide2, startX, startY + tileSize, 2);
    placeTile(jungleTileMap.platform.rightSide3, startX, startY + tileSize * 2, 2);

    console.log(`Created 1x3 right edge platform at (${startX}, ${startY})`);
  }

  function deleteTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const deletedTile = placedTiles[tileIndex];
      placedTiles.splice(tileIndex, 1);
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState(); // Save state for undo
      rebuildWorld();
      console.log(`Deleted tile ${deletedTile.id} at (${deletedTile.x}, ${deletedTile.y})`);
    }
  }

  function clearAllTiles() {
    worldBuilder.clearWorld();
    placedTiles = [];
  }

  function generateCode() {
    let code = '// Generated tile positioning code\n';
    code += '// Copy this into your createBasicWorld() or createFarmScene() function\n\n';
    
    placedTiles.forEach((tile, index) => {
      code += `worldBuilder.addTile(${tile.id}, ${tile.x}, ${tile.y}, ${tile.scale}); // Tile ${index + 1}\n`;
    });

    return code;
  }

  function exportCode() {
    const code = generateCode();
    navigator.clipboard.writeText(code).then(() => {
      alert('Code copied to clipboard!');
    }).catch(() => {
      // Fallback: show in alert
      alert('Copy this code:\n\n' + code);
    });
  }

  // Export/Import functionality
  function exportLayout() {
    const layoutData = {
      tiles: placedTiles,
      gridSize,
      worldSize: { width: screenWidth, height: screenHeight },
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0'
      }
    };

    const dataStr = JSON.stringify(layoutData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportName = `banana-world-layout-${new Date().toISOString().split('T')[0]}.json`;
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportName);
    linkElement.click();

    console.log('Layout exported:', exportName);
  }

  async function importLayout(file: File) {
    try {
      const text = await file.text();
      const layoutData = JSON.parse(text);

      // Validate data structure
      if (layoutData.tiles && Array.isArray(layoutData.tiles)) {
        // Save current state for undo before importing
        saveTileState();

        // Apply imported data
        gridSize = layoutData.gridSize || gridSize;
        placedTiles = layoutData.tiles;

        // Rebuild the world with imported tiles
        rebuildWorld();
        drawGrid();

        console.log('Layout imported successfully:', layoutData.metadata?.exportDate || 'Unknown date');
        alert(`Layout imported successfully! Loaded ${placedTiles.length} tiles.`);
      } else {
        throw new Error('Invalid layout file format');
      }
    } catch (error) {
      console.error('Failed to import layout:', error);
      alert('Failed to import layout. Please check the file format.');
    }
  }

  function handleFileImport(event: Event) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];
    if (file) {
      importLayout(file);
      // Reset input so same file can be imported again
      input.value = '';
    }
  }

  // Undo/Redo functionality
  function saveTileState() {
    const currentState = JSON.stringify(placedTiles);
    undoStack.push(currentState);

    // Limit undo stack size
    if (undoStack.length > maxUndoSteps) {
      undoStack.shift();
    }

    // Clear redo stack on new action
    redoStack = [];
  }

  function undo() {
    if (undoStack.length > 1) { // Keep at least one state (initial)
      const currentState = JSON.stringify(placedTiles);
      redoStack.push(currentState);

      // Remove current state and restore previous
      undoStack.pop();
      const previousState = undoStack[undoStack.length - 1];
      placedTiles = JSON.parse(previousState);
      rebuildWorld();

      console.log('Undo: Restored previous state');
    }
  }

  function redo() {
    if (redoStack.length > 0) {
      const currentState = JSON.stringify(placedTiles);
      undoStack.push(currentState);

      const nextState = redoStack.pop()!;
      placedTiles = JSON.parse(nextState);
      rebuildWorld();

      console.log('Redo: Restored next state');
    }
  }

  // Copy/Paste functionality
  function copyTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      copiedTile = { id: tile.id, scale: tile.scale };
      console.log(`Copied tile ${tile.id}`);
    }
  }

  function pasteTile(x: number, y: number) {
    if (copiedTile) {
      placeTile(copiedTile.id, x, y, copiedTile.scale);
      console.log(`Pasted tile ${copiedTile.id} at (${x}, ${y})`);
    }
  }

  // Tile transformation functions
  function rotateTile(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      tile.rotation = ((tile.rotation || 0) + 90) % 360;
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState();
      rebuildWorld();
      console.log(`Rotated tile ${tileIndex} to ${tile.rotation}°`);
    }
  }

  function flipTileX(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      tile.flipX = !tile.flipX;
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState();
      rebuildWorld();
      console.log(`Flipped tile ${tileIndex} horizontally: ${tile.flipX}`);
    }
  }

  function flipTileY(tileIndex: number) {
    if (tileIndex >= 0 && tileIndex < placedTiles.length) {
      const tile = placedTiles[tileIndex];
      tile.flipY = !tile.flipY;
      placedTiles = placedTiles; // Trigger reactivity
      saveTileState();
      rebuildWorld();
      console.log(`Flipped tile ${tileIndex} vertically: ${tile.flipY}`);
    }
  }

  // Selection management functions
  function toggleTileSelection(tileIndex: number) {
    if (selectedTileIndices.has(tileIndex)) {
      selectedTileIndices.delete(tileIndex);
    } else {
      selectedTileIndices.add(tileIndex);
    }
    selectedTileIndices = selectedTileIndices; // Trigger reactivity
    rebuildWorld(); // Update visual selection
    console.log(`Selection: ${Array.from(selectedTileIndices).join(', ')}`);
  }

  function clearSelection() {
    selectedTileIndices.clear();
    selectedTileIndices = selectedTileIndices; // Trigger reactivity
    rebuildWorld(); // Update visual selection
    console.log('Selection cleared');
  }

  function selectAllTiles() {
    selectedTileIndices.clear();
    placedTiles.forEach((_, index) => selectedTileIndices.add(index));
    selectedTileIndices = selectedTileIndices; // Trigger reactivity
    rebuildWorld(); // Update visual selection
    console.log(`Selected all ${selectedTileIndices.size} tiles`);
  }

  function deleteSelectedTiles() {
    if (selectedTileIndices.size === 0) return;

    // Convert to array and sort in descending order to maintain indices
    const indicesToDelete = Array.from(selectedTileIndices).sort((a, b) => b - a);

    indicesToDelete.forEach(index => {
      if (index >= 0 && index < placedTiles.length) {
        placedTiles.splice(index, 1);
      }
    });

    clearSelection();
    placedTiles = placedTiles; // Trigger reactivity
    saveTileState(); // Save state for undo
    rebuildWorld();
    console.log(`Deleted ${indicesToDelete.length} selected tiles`);
  }

  function duplicateSelection() {
    if (selectedTileIndices.size === 0) return;

    // Get selected tiles data
    const selectedTiles = Array.from(selectedTileIndices).map(index => ({
      ...placedTiles[index]
    }));

    // Find bounding box of selection
    const bounds = getSelectionBounds(selectedTiles);
    if (!bounds) return;

    // Try to find empty space nearby
    const newPosition = findEmptySpaceNear(bounds);
    if (!newPosition) {
      alert('No available space found nearby to place the duplicate.');
      return;
    }

    // Calculate offset from original position
    const offsetX = newPosition.x - bounds.minX;
    const offsetY = newPosition.y - bounds.minY;

    // Create duplicated tiles with new positions
    const newTileIndices: number[] = [];
    selectedTiles.forEach(tile => {
      const newTile = {
        ...tile,
        x: tile.x + offsetX,
        y: tile.y + offsetY
      };

      placedTiles.push(newTile);
      newTileIndices.push(placedTiles.length - 1);
    });

    // Select the new duplicated tiles
    clearSelection();
    newTileIndices.forEach(index => selectedTileIndices.add(index));
    selectedTileIndices = selectedTileIndices; // Trigger reactivity

    placedTiles = placedTiles; // Trigger reactivity
    saveTileState(); // Save state for undo
    rebuildWorld();

    console.log(`Duplicated ${selectedTiles.length} tiles to (${newPosition.x}, ${newPosition.y})`);
  }

  function getSelectionBounds(tiles: typeof placedTiles) {
    if (tiles.length === 0) return null;

    let minX = tiles[0].x;
    let maxX = tiles[0].x;
    let minY = tiles[0].y;
    let maxY = tiles[0].y;

    tiles.forEach(tile => {
      minX = Math.min(minX, tile.x);
      maxX = Math.max(maxX, tile.x);
      minY = Math.min(minY, tile.y);
      maxY = Math.max(maxY, tile.y);
    });

    return {
      minX,
      maxX,
      minY,
      maxY,
      width: maxX - minX + gridSize,
      height: maxY - minY + gridSize
    };
  }

  function findEmptySpaceNear(bounds: {minX: number, maxX: number, minY: number, maxY: number, width: number, height: number}) {
    const searchRadius = 5; // Search within 5 grid units
    const positions = [];

    // Generate potential positions in expanding rings around the selection
    for (let ring = 1; ring <= searchRadius; ring++) {
      // Right side
      positions.push({
        x: bounds.maxX + gridSize,
        y: bounds.minY
      });

      // Left side
      positions.push({
        x: bounds.minX - bounds.width,
        y: bounds.minY
      });

      // Bottom
      positions.push({
        x: bounds.minX,
        y: bounds.maxY + gridSize
      });

      // Top
      positions.push({
        x: bounds.minX,
        y: bounds.minY - bounds.height
      });

      // Diagonal positions
      positions.push({
        x: bounds.maxX + gridSize,
        y: bounds.maxY + gridSize
      });

      positions.push({
        x: bounds.minX - bounds.width,
        y: bounds.minY - bounds.height
      });

      positions.push({
        x: bounds.maxX + gridSize,
        y: bounds.minY - bounds.height
      });

      positions.push({
        x: bounds.minX - bounds.width,
        y: bounds.maxY + gridSize
      });

      // Expand search area for next ring
      bounds = {
        ...bounds,
        minX: bounds.minX - gridSize,
        maxX: bounds.maxX + gridSize,
        minY: bounds.minY - gridSize,
        maxY: bounds.maxY + gridSize,
        width: bounds.width + gridSize * 2,
        height: bounds.height + gridSize * 2
      };
    }

    // Check each position to see if it's empty
    for (const pos of positions) {
      if (isAreaEmpty(pos.x, pos.y, bounds.width - gridSize * 2, bounds.height - gridSize * 2)) {
        return pos;
      }
    }

    return null; // No empty space found
  }

  function isAreaEmpty(startX: number, startY: number, width: number, height: number): boolean {
    // Check if area is within reasonable bounds (prevent placing too far off-screen)
    if (startX < -gridSize * 2 || startY < -gridSize * 2 ||
        startX > screenWidth + gridSize * 2 || startY > screenHeight + gridSize * 2) {
      return false;
    }

    // Check if any existing tiles overlap with this area
    for (const tile of placedTiles) {
      if (tile.x >= startX && tile.x < startX + width &&
          tile.y >= startY && tile.y < startY + height) {
        return false;
      }
    }

    return true;
  }

  // Layer management functions
  function setActiveLayer(index: number) {
    if (index >= 0 && index < layers.length) {
      activeLayerIndex = index;
      console.log(`Active layer set to: ${layers[index].name}`);
    }
  }

  function toggleLayerVisibility(index: number) {
    if (index >= 0 && index < layers.length) {
      layers[index].visible = !layers[index].visible;
      layers[index].container.visible = layers[index].visible;
      layers = layers; // Trigger reactivity
      console.log(`Layer ${layers[index].name} visibility: ${layers[index].visible}`);
    }
  }

  // Panel dragging functions
  function startPanelDrag(event: MouseEvent) {
    isDraggingPanel = true;
    panelDragStartX = event.clientX - panelX;
    panelDragStartY = event.clientY - panelY;

    // Add global mouse move and up listeners
    document.addEventListener('mousemove', handlePanelDrag);
    document.addEventListener('mouseup', stopPanelDrag);

    // Prevent text selection during drag
    event.preventDefault();
  }

  function handlePanelDrag(event: MouseEvent) {
    if (!isDraggingPanel) return;

    panelX = event.clientX - panelDragStartX;
    panelY = event.clientY - panelDragStartY;

    // Keep panel within screen bounds
    panelX = Math.max(0, Math.min(panelX, window.innerWidth - 420));
    panelY = Math.max(0, Math.min(panelY, window.innerHeight - 200));
  }

  function stopPanelDrag() {
    isDraggingPanel = false;

    // Remove global listeners
    document.removeEventListener('mousemove', handlePanelDrag);
    document.removeEventListener('mouseup', stopPanelDrag);
  }

  // Keyboard shortcuts
  function setupKeyboardShortcuts() {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!visible) return;

      // Undo: Ctrl+Z
      if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undo();
      }

      // Redo: Ctrl+Y or Ctrl+Shift+Z
      if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
        e.preventDefault();
        redo();
      }

      // Delete: Del key when hovering over tile
      if (e.key === 'Delete' && hoveredTileIndex >= 0) {
        e.preventDefault();
        deleteTile(hoveredTileIndex);
      }

      // Copy: Ctrl+C to copy selected tile properties
      if (e.ctrlKey && e.key === 'c' && hoveredTileIndex >= 0) {
        e.preventDefault();
        copyTile(hoveredTileIndex);
      }

      // Paste: Ctrl+V to paste tile at cursor position
      if (e.ctrlKey && e.key === 'v') {
        e.preventDefault();
        pasteTile(snappedX, snappedY);
      }

      // Rotate: R key when hovering over tile
      if (e.key === 'r' && hoveredTileIndex >= 0) {
        e.preventDefault();
        rotateTile(hoveredTileIndex);
      }

      // Flip horizontally: H key when hovering over tile
      if (e.key === 'h' && hoveredTileIndex >= 0) {
        e.preventDefault();
        flipTileX(hoveredTileIndex);
      }

      // Flip vertically: V key when hovering over tile
      if (e.key === 'v' && hoveredTileIndex >= 0 && !e.ctrlKey) {
        e.preventDefault();
        flipTileY(hoveredTileIndex);
      }

      // Select all: Ctrl+A
      if (e.ctrlKey && e.key === 'a') {
        e.preventDefault();
        selectAllTiles();
      }

      // Clear selection: Escape
      if (e.key === 'Escape') {
        e.preventDefault();
        clearSelection();
      }

      // Delete selected: Delete key when selection exists
      if (e.key === 'Delete' && selectedTileIndices.size > 0 && hoveredTileIndex < 0) {
        e.preventDefault();
        deleteSelectedTiles();
      }

      // Duplicate selection: Ctrl+D
      if (e.ctrlKey && e.key === 'd' && selectedTileIndices.size > 0) {
        e.preventDefault();
        duplicateSelection();
      }
    };

    // Store reference for cleanup and add listener
    keydownHandler = handleKeyDown;
    window.addEventListener('keydown', keydownHandler);
  }

  function cleanup() {
    // Remove keyboard event listener
    if (keydownHandler) {
      window.removeEventListener('keydown', keydownHandler);
      keydownHandler = null;
    }

    if (pixiApp) {
      pixiApp.destroy(true);
      pixiApp = null as any;
    }
    toolInitialized = false;
  }

  // Reactive updates
  $: if (toolInitialized && showGrid !== undefined) {
    drawGrid();
  }

  $: if (toolInitialized && gridSize) {
    drawGrid();
  }

  $: if (toolInitialized && showTileBorders !== undefined) {
    rebuildWorld();
  }
</script>

{#if visible}
<div class="tile-tool-overlay">
  <!-- Old controls (hidden) -->
  <div class="tool-controls" style="display: none;">
    {#if selectedCategory === 'platform'}
      <div class="control-group">
        <label>
          Platform Part:
          <select bind:value={selectedTileType}>
            <option value="topLeft">Top Left Corner</option>
            <option value="topCenter1">Top Center 1</option>
            <option value="topCenter2">Top Center 2</option>
            <option value="topCenter3">Top Center 3</option>
            <option value="topRight">Top Right Corner</option>
            <option value="leftSide1">Left Side 1</option>
            <option value="leftSide2">Left Side 2</option>
            <option value="leftSide3">Left Side 3</option>
            <option value="rightSide1">Right Side 1</option>
            <option value="rightSide2">Right Side 2</option>
            <option value="rightSide3">Right Side 3</option>
            <option value="bottomLeft">Bottom Left Corner</option>
            <option value="bottomCenter1">Bottom Center 1</option>
            <option value="bottomCenter2">Bottom Center 2</option>
            <option value="bottomCenter3">Bottom Center 3</option>
            <option value="bottomRight">Bottom Right Corner</option>
          </select>
        </label>
      </div>
    {:else if selectedCategory === 'foliage'}
      <div class="control-group">
        <label>
          Foliage Type:
          <select bind:value={selectedTileType}>
            <option value="topLeft">Top Left</option>
            <option value="topCenter">Top Center</option>
            <option value="topRight">Top Right</option>
            <option value="centerLeft">Center Left</option>
            <option value="center">Center</option>
            <option value="centerRight">Center Right</option>
            <option value="bottomLeft">Bottom Left</option>
            <option value="bottomCenter">Bottom Center</option>
            <option value="bottomRight">Bottom Right</option>
          </select>
        </label>
      </div>
    {:else}
      <div class="control-group">
        <label>
          Tile ID:
          <select bind:value={selectedTileId}>
            {#each availableTiles as tileId}
              <option value={tileId}>Tile {tileId}</option>
            {/each}
          </select>
        </label>
      </div>
    {/if}

    <div class="control-group">
      <label>
        Grid Size:
        <input type="range" min="16" max="64" step="16" bind:value={gridSize} />
        <span>{gridSize}px</span>
      </label>
    </div>

    <div class="control-group">
      <label>
        <input type="checkbox" bind:checked={showGrid} />
        Show Grid
      </label>
    </div>

    <div class="control-group">
      <label>
        <input type="checkbox" bind:checked={showTileBorders} />
        Show Tile Borders
      </label>
    </div>

    <div class="control-group layer-controls">
      <span class="control-label">Active Layer:</span>
      {#each layers as layer, index}
        <button
          class="layer-button"
          class:active={activeLayerIndex === index}
          on:click={() => setActiveLayer(index)}
        >
          {layer.name}
        </button>
      {/each}
    </div>

    <div class="control-group layer-visibility">
      <span class="control-label">Layer Visibility:</span>
      {#each layers as layer, index}
        <label class="layer-visibility-item">
          <input
            type="checkbox"
            bind:checked={layer.visible}
            on:change={() => toggleLayerVisibility(index)}
          />
          {layer.name}
        </label>
      {/each}
    </div>

    <div class="control-group">
      <button on:click={undo} disabled={undoStack.length <= 1}>Undo (Ctrl+Z)</button>
      <button on:click={redo} disabled={redoStack.length === 0}>Redo (Ctrl+Y)</button>
      <button on:click={clearAllTiles}>Clear All</button>
      <button on:click={exportCode}>Export Code</button>
    </div>

    <div class="control-group selection-controls">
      <span class="control-label">Selection:</span>
      <button on:click={selectAllTiles}>Select All (Ctrl+A)</button>
      <button on:click={clearSelection} disabled={selectedTileIndices.size === 0}>
        Clear Selection (Esc)
      </button>
      <button on:click={duplicateSelection} disabled={selectedTileIndices.size === 0}>
        🔄 Duplicate (Ctrl+D) ({selectedTileIndices.size})
      </button>
      <button on:click={deleteSelectedTiles} disabled={selectedTileIndices.size === 0}>
        🗑️ Delete ({selectedTileIndices.size})
      </button>
    </div>

    <div class="control-group smart-placement">
      <span class="control-label">Smart Placement:</span>
      <button on:click={() => createJunglePlatform(snappedX, snappedY, 4, 3)}>
        4x3 Platform
      </button>
      <button on:click={() => createJunglePlatform(snappedX, snappedY, 6, 2)}>
        6x2 Platform
      </button>
      <button on:click={() => create5x1Platform(snappedX, snappedY)}>
        5x1 Top Edge
      </button>
      <button on:click={() => create5x1BottomPlatform(snappedX, snappedY)}>
        5x1 Bottom Edge
      </button>
      <button on:click={() => create1x3LeftPlatform(snappedX, snappedY)}>
        1x3 Left Edge
      </button>
      <button on:click={() => create1x3RightPlatform(snappedX, snappedY)}>
        1x3 Right Edge
      </button>
      <button on:click={() => createHorizontalPlatform(snappedX, snappedY, 8)}>
        8-Tile Bridge
      </button>
      <button on:click={() => createVerticalPlatform(snappedX, snappedY, 5)}>
        5-Tile Tower
      </button>
      <button on:click={() => createStaircase(snappedX, snappedY, 5, 'up')}>
        5-Step Stairs ↗
      </button>
      <button on:click={() => createStaircase(snappedX, snappedY, 5, 'down')}>
        5-Step Stairs ↘
      </button>
    </div>

    <div class="control-group">
      <button on:click={exportLayout}>💾 Export Layout</button>
      <label class="file-input-label">
        📁 Import Layout
        <input type="file" accept=".json" on:change={handleFileImport} style="display: none;" />
      </label>
    </div>

    {#if hoveredTileIndex >= 0}
      <div class="control-group tile-transform-controls">
        <span>Transform Hovered Tile:</span>
        <button on:click={() => rotateTile(hoveredTileIndex)}>🔄 Rotate (R)</button>
        <button on:click={() => flipTileX(hoveredTileIndex)}>↔️ Flip H (H)</button>
        <button on:click={() => flipTileY(hoveredTileIndex)}>↕️ Flip V (V)</button>
      </div>
    {/if}
  </div>
  </div> <!-- Close tool-controls -->

  <!-- Floating Control Panel -->
  {#if showFloatingPanel}
    <div class="floating-panel" style="left: {panelX}px; top: {panelY}px;">
      <!-- Panel Header with Tabs -->
      <div class="panel-header">
        <div class="panel-drag-handle" role="button" tabindex="0" on:mousedown={startPanelDrag}>
          ⋮⋮ Tile Tools
        </div>
        <div class="panel-tabs">
          <button
            class="tab-button"
            class:active={activeTab === 'placement'}
            on:click={() => activeTab = 'placement'}
          >
            🎨 Placement
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'selection'}
            on:click={() => activeTab = 'selection'}
          >
            ✋ Selection
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'layers'}
            on:click={() => activeTab = 'layers'}
          >
            📚 Layers
          </button>
          <button
            class="tab-button"
            class:active={activeTab === 'smart'}
            on:click={() => activeTab = 'smart'}
          >
            🤖 Smart
          </button>
        </div>
        <button class="panel-toggle" on:click={() => showFloatingPanel = false}>✕</button>
      </div>

      <!-- Panel Content -->
      <div class="panel-content">
        {#if activeTab === 'placement'}
          <div class="tab-panel">
            <div class="control-group">
              <label>
                Tile Category:
                <select bind:value={selectedCategory}>
                  <option value="platform">Platform Structure</option>
                  <option value="foliage">Jungle Foliage</option>
                  <option value="custom">Custom Tile ID</option>
                </select>
              </label>
            </div>

            {#if selectedCategory === 'platform'}
              <div class="control-group">
                <label>
                  Platform Part:
                  <select bind:value={selectedTileType}>
                    <option value="topLeft">Top Left Corner</option>
                    <option value="topCenter1">Top Center 1</option>
                    <option value="topCenter2">Top Center 2</option>
                    <option value="topCenter3">Top Center 3</option>
                    <option value="topRight">Top Right Corner</option>
                    <option value="leftSide1">Left Side 1</option>
                    <option value="leftSide2">Left Side 2</option>
                    <option value="leftSide3">Left Side 3</option>
                    <option value="rightSide1">Right Side 1</option>
                    <option value="rightSide2">Right Side 2</option>
                    <option value="rightSide3">Right Side 3</option>
                    <option value="bottomLeft">Bottom Left Corner</option>
                    <option value="bottomCenter1">Bottom Center 1</option>
                    <option value="bottomCenter2">Bottom Center 2</option>
                    <option value="bottomCenter3">Bottom Center 3</option>
                    <option value="bottomRight">Bottom Right Corner</option>
                  </select>
                </label>
              </div>
            {:else if selectedCategory === 'foliage'}
              <div class="control-group">
                <label>
                  Foliage Type:
                  <select bind:value={selectedTileType}>
                    <option value="topLeft">Top Left</option>
                    <option value="topCenter">Top Center</option>
                    <option value="topRight">Top Right</option>
                    <option value="centerLeft">Center Left</option>
                    <option value="center">Center</option>
                    <option value="centerRight">Center Right</option>
                    <option value="bottomLeft">Bottom Left</option>
                    <option value="bottomCenter">Bottom Center</option>
                    <option value="bottomRight">Bottom Right</option>
                  </select>
                </label>
              </div>
            {:else}
              <div class="control-group">
                <label>
                  Tile ID:
                  <select bind:value={selectedTileId}>
                    {#each availableTiles as tileId}
                      <option value={tileId}>Tile {tileId}</option>
                    {/each}
                  </select>
                </label>
              </div>
            {/if}

            <div class="control-group">
              <label>
                Grid Size:
                <input type="range" min="16" max="64" step="16" bind:value={gridSize} />
                <span>{gridSize}px</span>
              </label>
            </div>

            <div class="control-group">
              <label>
                <input type="checkbox" bind:checked={showGrid} />
                Show Grid
              </label>
            </div>

            <div class="control-group">
              <label>
                <input type="checkbox" bind:checked={showTileBorders} />
                Show Tile Borders
              </label>
            </div>

            <div class="control-group">
              <button on:click={undo} disabled={undoStack.length <= 1}>Undo (Ctrl+Z)</button>
              <button on:click={redo} disabled={redoStack.length === 0}>Redo (Ctrl+Y)</button>
              <button on:click={clearAllTiles}>Clear All</button>
              <button on:click={exportCode}>Export Code</button>
            </div>

            <div class="control-group">
              <button on:click={exportLayout}>💾 Export Layout</button>
              <label class="file-input-label">
                📁 Import Layout
                <input type="file" accept=".json" on:change={handleFileImport} style="display: none;" />
              </label>
            </div>
          </div>
        {:else if activeTab === 'selection'}
          <div class="tab-panel">
            <div class="control-group selection-controls">
              <button on:click={selectAllTiles}>Select All (Ctrl+A)</button>
              <button on:click={clearSelection} disabled={selectedTileIndices.size === 0}>
                Clear Selection (Esc)
              </button>
              <button on:click={duplicateSelection} disabled={selectedTileIndices.size === 0}>
                🔄 Duplicate (Ctrl+D) ({selectedTileIndices.size})
              </button>
              <button on:click={deleteSelectedTiles} disabled={selectedTileIndices.size === 0}>
                🗑️ Delete ({selectedTileIndices.size})
              </button>
            </div>

            {#if hoveredTileIndex >= 0}
              <div class="control-group tile-transform-controls">
                <span>Transform Hovered Tile:</span>
                <button on:click={() => rotateTile(hoveredTileIndex)}>🔄 Rotate (R)</button>
                <button on:click={() => flipTileX(hoveredTileIndex)}>↔️ Flip H (H)</button>
                <button on:click={() => flipTileY(hoveredTileIndex)}>↕️ Flip V (V)</button>
              </div>
            {/if}
          </div>
        {:else if activeTab === 'layers'}
          <div class="tab-panel">
            <div class="control-group layer-controls">
              <span class="control-label">Active Layer:</span>
              {#each layers as layer, index}
                <button
                  class="layer-button"
                  class:active={activeLayerIndex === index}
                  on:click={() => setActiveLayer(index)}
                >
                  {layer.name}
                </button>
              {/each}
            </div>

            <div class="control-group layer-visibility">
              <span class="control-label">Layer Visibility:</span>
              {#each layers as layer, index}
                <label class="layer-visibility-item">
                  <input
                    type="checkbox"
                    bind:checked={layer.visible}
                    on:change={() => toggleLayerVisibility(index)}
                  />
                  {layer.name}
                </label>
              {/each}
            </div>
          </div>
        {:else if activeTab === 'smart'}
          <div class="tab-panel">
            <div class="control-group smart-placement">
              <button on:click={() => createJunglePlatform(snappedX, snappedY, 4, 3)}>
                4x3 Platform
              </button>
              <button on:click={() => createJunglePlatform(snappedX, snappedY, 6, 2)}>
                6x2 Platform
              </button>
              <button on:click={() => create5x1Platform(snappedX, snappedY)}>
                5x1 Top Edge
              </button>
              <button on:click={() => create5x1BottomPlatform(snappedX, snappedY)}>
                5x1 Bottom Edge
              </button>
              <button on:click={() => create1x3LeftPlatform(snappedX, snappedY)}>
                1x3 Left Edge
              </button>
              <button on:click={() => create1x3RightPlatform(snappedX, snappedY)}>
                1x3 Right Edge
              </button>
              <button on:click={() => createHorizontalPlatform(snappedX, snappedY, 8)}>
                8-Tile Bridge
              </button>
              <button on:click={() => createVerticalPlatform(snappedX, snappedY, 5)}>
                5-Tile Tower
              </button>
              <button on:click={() => createStaircase(snappedX, snappedY, 5, 'up')}>
                5-Step Stairs ↗
              </button>
              <button on:click={() => createStaircase(snappedX, snappedY, 5, 'down')}>
                5-Step Stairs ↘
              </button>
            </div>
          </div>
        {/if}
      </div>
    </div>
  {/if}

  <!-- Panel Toggle Button (when panel is hidden) -->
  {#if !showFloatingPanel}
    <button class="panel-show-button" on:click={() => showFloatingPanel = true}>
      ⚙️ Tools
    </button>
  {/if}

  <!-- Canvas Container -->
  <div class="canvas-container" bind:this={canvasContainer}></div>

  <!-- Status Info -->
  <div class="status-info">
    <div>Mouse: ({mouseX}, {mouseY})</div>
    <div>Snapped: ({snappedX}, {snappedY})</div>
    <div>Tiles Placed: {placedTiles.length}</div>
    <div>Selected Tile: {getSelectedTileId()} ({selectedCategory} - {selectedTileType})</div>
    <div>Active Layer: {layers[activeLayerIndex].name}</div>
    <div>Selection: {selectedTileIndices.size} tiles</div>
    <div>Undo: {undoStack.length - 1} | Redo: {redoStack.length}</div>
    {#if hoveredTileIndex >= 0}
      {@const tile = placedTiles[hoveredTileIndex]}
      <div>Hovered: Tile {tile.id} | Rot: {tile.rotation || 0}° | FlipX: {tile.flipX || false} | FlipY: {tile.flipY || false}</div>
    {/if}
    {#if copiedTile}
      <div class="copy-status">📋 Copied: Tile {copiedTile.id}</div>
    {/if}
    {#if isDragging}
      <div class="drag-status">🖱️ Dragging tile {draggedTileIndex + 1}</div>
    {/if}
  </div>

  <!-- Instructions -->
  <div class="instructions-bar">
    <div class="instruction">🖱️ Left Click: Place tile</div>
    <div class="instruction">🖱️ Ctrl+Left Click: Select/deselect tile</div>
    <div class="instruction">🖱️ Middle Click + Drag: Move tile/selection</div>
    <div class="instruction">🖱️ Right Click: Delete tile/selection</div>
    <div class="instruction">⌨️ Ctrl+A: Select all</div>
    <div class="instruction">⌨️ Esc: Clear selection</div>
    <div class="instruction">⌨️ Ctrl+D: Duplicate selection</div>
    <div class="instruction">⌨️ Ctrl+Z: Undo</div>
    <div class="instruction">⌨️ Ctrl+Y: Redo</div>
    <div class="instruction">⌨️ Del: Delete hovered/selected</div>
    <div class="instruction">⌨️ Ctrl+C: Copy tile</div>
    <div class="instruction">⌨️ Ctrl+V: Paste tile</div>
    <div class="instruction">⌨️ R: Rotate hovered tile</div>
    <div class="instruction">⌨️ H: Flip hovered tile horizontally</div>
    <div class="instruction">⌨️ V: Flip hovered tile vertically</div>
  </div>
{/if}

<style>
  .tile-tool-overlay {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #2c3e50;
    z-index: 1000;
    display: flex;
    flex-direction: column;
  }

  .tool-controls {
    background: #34495e;
    padding: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    color: white;
  }

  .control-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .control-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
  }

  .control-group select,
  .control-group input[type="range"] {
    padding: 0.25rem;
    border: 1px solid #7f8c8d;
    border-radius: 4px;
    background: #ecf0f1;
  }

  .control-group button {
    padding: 0.5rem 1rem;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .control-group button:hover {
    background: #2980b9;
  }

  .tile-transform-controls {
    background: #2c3e50;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #34495e;
  }

  .tile-transform-controls span {
    color: #ecf0f1;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .file-input-label {
    padding: 0.5rem 1rem;
    background: #27ae60;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    display: inline-block;
  }

  .file-input-label:hover {
    background: #229954;
  }

  .control-label {
    color: #ecf0f1;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  .layer-controls {
    background: #2c3e50;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #34495e;
  }

  .layer-button {
    padding: 0.25rem 0.75rem;
    margin: 0 0.25rem;
    background: #7f8c8d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
  }

  .layer-button:hover {
    background: #95a5a6;
  }

  .layer-button.active {
    background: #3498db;
    font-weight: bold;
  }

  .layer-visibility {
    background: #2c3e50;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #34495e;
  }

  .layer-visibility-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin: 0 0.5rem;
    color: #ecf0f1;
    font-size: 0.8rem;
  }

  .smart-placement {
    background: #8e44ad;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #9b59b6;
    flex-wrap: wrap;
  }

  .smart-placement button {
    background: #9b59b6;
    margin: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }

  .smart-placement button:hover {
    background: #8e44ad;
  }

  .selection-controls {
    background: #16a085;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #1abc9c;
    flex-wrap: wrap;
  }

  .selection-controls button {
    background: #1abc9c;
    margin: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
  }

  .selection-controls button:hover:not(:disabled) {
    background: #16a085;
  }

  .selection-controls button:disabled {
    background: #7f8c8d;
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* Floating Panel Styles */
  .floating-panel {
    position: fixed;
    width: 420px;
    max-height: calc(100vh - 200px);
    background: #2c3e50;
    border: 2px solid #34495e;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    overflow: hidden;
    user-select: none;
  }

  .panel-header {
    background: #34495e;
    padding: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #7f8c8d;
  }

  .panel-drag-handle {
    color: #bdc3c7;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: move;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    user-select: none;
    min-width: 80px;
  }

  .panel-drag-handle:hover {
    background: #2c3e50;
    color: #ecf0f1;
  }

  .panel-drag-handle:active {
    cursor: grabbing;
    background: #1a252f;
  }

  .panel-tabs {
    display: flex;
    gap: 0.25rem;
  }

  .tab-button {
    padding: 0.5rem 0.75rem;
    background: #7f8c8d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.2s;
  }

  .tab-button:hover {
    background: #95a5a6;
  }

  .tab-button.active {
    background: #3498db;
    font-weight: bold;
  }

  .panel-toggle {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
  }

  .panel-toggle:hover {
    background: #c0392b;
  }

  .panel-content {
    max-height: calc(100vh - 280px);
    overflow-y: auto;
    padding: 1rem;
  }

  .tab-panel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .panel-show-button {
    position: fixed;
    top: 20px;
    left: 20px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
  }

  .panel-show-button:hover {
    background: #2980b9;
  }

  .canvas-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    z-index: 1;
    min-height: 400px; /* Ensure minimum height */
  }

  .status-info {
    background: #34495e;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 2rem;
    color: white;
    font-size: 0.8rem;
    font-family: monospace;
    align-items: center;
  }

  .drag-status {
    background: #e74c3c;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
    animation: pulse 1s infinite;
  }

  .copy-status {
    background: #27ae60;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-weight: bold;
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  .instructions-bar {
    background: #2c3e50;
    padding: 0.5rem 1rem;
    display: flex;
    gap: 2rem;
    color: #bdc3c7;
    font-size: 0.75rem;
    border-top: 1px solid #34495e;
  }

  .instruction {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
</style>
